-- Add Emergency Mode columns to profiles table
-- Run this in your Supabase SQL Editor to add emergency mode functionality
-- Date: 2025-01-09

-- Check if the emergency mode columns already exist and add them if they don't
DO $$
BEGIN
    -- Add emergency_mode column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'profiles'
        AND column_name = 'emergency_mode'
    ) THEN
        ALTER TABLE public.profiles
        ADD COLUMN emergency_mode BOOLEAN DEFAULT false NOT NULL;
        
        RAISE NOTICE 'Added emergency_mode column to profiles table';
    ELSE
        RAISE NOTICE 'emergency_mode column already exists in profiles table';
    END IF;

    -- Add emergency_start_time column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'profiles'
        AND column_name = 'emergency_start_time'
    ) THEN
        ALTER TABLE public.profiles
        ADD COLUMN emergency_start_time TIMESTAMP WITH TIME ZONE;
        
        RAISE NOTICE 'Added emergency_start_time column to profiles table';
    ELSE
        RAISE NOTICE 'emergency_start_time column already exists in profiles table';
    END IF;

    -- Add emergency_expiry_time column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'profiles'
        AND column_name = 'emergency_expiry_time'
    ) THEN
        ALTER TABLE public.profiles
        ADD COLUMN emergency_expiry_time TIMESTAMP WITH TIME ZONE;
        
        RAISE NOTICE 'Added emergency_expiry_time column to profiles table';
    ELSE
        RAISE NOTICE 'emergency_expiry_time column already exists in profiles table';
    END IF;

    -- Add exception_groups column (JSON array of group names that can still contact the user)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'profiles'
        AND column_name = 'exception_groups'
    ) THEN
        ALTER TABLE public.profiles
        ADD COLUMN exception_groups JSONB DEFAULT '[]'::jsonb;

        RAISE NOTICE 'Added exception_groups column to profiles table';
    ELSE
        RAISE NOTICE 'exception_groups column already exists in profiles table';
    END IF;
END $$;

-- Create index for emergency mode queries (for performance)
CREATE INDEX IF NOT EXISTS idx_profiles_emergency_mode ON public.profiles(emergency_mode) 
WHERE emergency_mode = true;

-- Create index for emergency expiry time (for cleanup queries)
CREATE INDEX IF NOT EXISTS idx_profiles_emergency_expiry ON public.profiles(emergency_expiry_time) 
WHERE emergency_mode = true AND emergency_expiry_time IS NOT NULL;

-- Update RLS policies to allow other users to read emergency status
-- This is needed so other users can check if someone is in emergency mode

-- Drop existing policy if it exists
DROP POLICY IF EXISTS "Users can view emergency status of others" ON public.profiles;

-- Create policy to allow reading emergency status fields of other users
CREATE POLICY "Users can view emergency status of others" ON public.profiles
    FOR SELECT USING (
        -- Users can always see their own profile
        auth.uid() = id 
        OR 
        -- Users can see emergency status fields of other users (but not other private data)
        (auth.uid() IS NOT NULL)
    );

-- Function to automatically clean up expired emergency modes
CREATE OR REPLACE FUNCTION cleanup_expired_emergency_modes()
RETURNS INTEGER AS $$
DECLARE
    cleaned_count INTEGER := 0;
BEGIN
    -- Update profiles where emergency mode has expired
    UPDATE public.profiles
    SET 
        emergency_mode = false,
        emergency_start_time = NULL,
        emergency_expiry_time = NULL,
        emergency_reason = NULL,
        updated_at = NOW()
    WHERE 
        emergency_mode = true 
        AND emergency_expiry_time IS NOT NULL 
        AND emergency_expiry_time < NOW();
    
    GET DIAGNOSTICS cleaned_count = ROW_COUNT;
    
    RAISE NOTICE 'Cleaned up % expired emergency modes', cleaned_count;
    RETURN cleaned_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a scheduled function to run cleanup (you can set this up in Supabase cron jobs)
-- This is just the function definition - you'll need to set up the actual cron job in Supabase dashboard

-- Final success message
DO $$
BEGIN
    RAISE NOTICE '✅ Emergency mode columns and functions added successfully!';
    RAISE NOTICE '';
    RAISE NOTICE 'Added columns:';
    RAISE NOTICE '- emergency_mode (BOOLEAN)';
    RAISE NOTICE '- emergency_start_time (TIMESTAMP)';
    RAISE NOTICE '- emergency_expiry_time (TIMESTAMP)';
    RAISE NOTICE '- emergency_reason (TEXT)';
    RAISE NOTICE '';
    RAISE NOTICE 'Added indexes for performance optimization';
    RAISE NOTICE 'Updated RLS policies to allow emergency status checking';
    RAISE NOTICE 'Created cleanup function for expired emergency modes';
    RAISE NOTICE '';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Set up a cron job in Supabase to run cleanup_expired_emergency_modes() every hour';
    RAISE NOTICE '2. Test the emergency mode functionality in your app';
END $$;
