import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'category.dart';

extension CategoryTypeExtensions on CategoryType {
  String getDisplayName(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case CategoryType.contactAnytime:
        return l10n.categoryContactAnytime;
      case CategoryType.preferAnytime:
        return l10n.categoryPreferAnytime;
      case CategoryType.contactAtTimes:
        return l10n.categoryContactAtTimes;
      case CategoryType.contactThroughMessages:
        return l10n.categoryContactThroughMessages;
    }
  }

  String getDefaultNote(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case CategoryType.contactAnytime:
        return l10n.categoryNoteContactAnytime;
      case CategoryType.preferAnytime:
        return l10n.categoryNotePreferAnytime;
      case CategoryType.contactAtTimes:
        return l10n.categoryNoteContactAtTimes;
      case CategoryType.contactThroughMessages:
        return l10n.categoryNoteContactThroughMessages;
    }
  }

  Color getColor() {
    switch (this) {
      case CategoryType.contactAnytime:
        return Colors.green;
      case CategoryType.preferAnytime:
        return Colors.blue;
      case CategoryType.contactAtTimes:
        return Colors.orange;
      case CategoryType.contactThroughMessages:
        return Colors.purple;
    }
  }
}

extension CategoryExtensions on Category {
  String getDefaultNote(BuildContext context) {
    return type.getDefaultNote(context);
  }
}
