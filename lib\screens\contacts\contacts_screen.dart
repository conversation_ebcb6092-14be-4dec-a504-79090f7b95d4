// ignore_for_file: avoid_print

import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart' hide Category;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../models/models.dart';
import '../../models/category_extensions.dart';
import '../../services/supabase_service.dart';
import '../../services/contacts_service.dart';
import '../../services/calling_service.dart';
import '../../services/notification_service.dart';
import '../../services/offline_contact_service.dart';
import '../../services/connectivity_service.dart';
import '../../services/data_stream_service.dart';
import '../../services/language_service.dart';
import '../../services/emergency_mode_service.dart';
import '../../widgets/ltr_override.dart';
import 'contact_detail_screen.dart';

class ContactsScreen extends StatefulWidget {
  const ContactsScreen({super.key});

  @override
  State<ContactsScreen> createState() => _ContactsScreenState();
}

class _ContactsScreenState extends State<ContactsScreen> {
  List<ContactWithProfile> _contactsUsingApp = [];
  List<ContactWithProfile> _filteredContacts = [];
  DataLoadingState _loadingState = DataLoadingState.initial;
  String? _error;

  // Search functionality
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  bool _isSearchActive = false;
  String _searchQuery = '';
  List<String> _searchHistory = [];
  bool _showSearchHistory = false;

  // Stream-based data service
  final DataStreamService _dataStreamService = DataStreamService();

  // Emergency mode service
  final EmergencyModeService _emergencyService = EmergencyModeService();
  bool _isEmergencyMode = false;

  @override
  void initState() {
    super.initState();
    _initializeStreamService();
    _loadSearchHistory();
    _setupSearchListeners();
    _initializeEmergencyService();
    _setupConnectivityListener();
    // Clear emergency cache on app start to ensure fresh data
    EmergencyModeService.clearAllCache();
  }

  Future<void> _initializeEmergencyService() async {
    await _emergencyService.initialize();

    // Listen to emergency mode changes
    _emergencyService.emergencyModeStream.listen((isEmergency) {
      if (mounted) {
        setState(() {
          _isEmergencyMode = isEmergency;
        });
        print('🔄 Emergency mode state changed: $isEmergency');
      }
    });

    // Set initial state
    setState(() {
      _isEmergencyMode = _emergencyService.isEmergencyMode;
    });

    // Debug: Check current emergency status
    _debugCheckEmergencyStatus();
  }

  Future<void> _debugCheckEmergencyStatus() async {
    try {
      final currentProfile = await OfflineContactService().getCurrentProfile();
      if (currentProfile != null) {
        final status = await EmergencyModeService.checkUserEmergencyStatus(
            currentProfile.id);
        print(
            '🔍 DEBUG - Current user emergency status: ${status?.isActive ?? false}');
        print('🔍 DEBUG - Local emergency mode: $_isEmergencyMode');
      }
    } catch (e) {
      print('❌ DEBUG - Error checking emergency status: $e');
    }
  }

  void _setupConnectivityListener() {
    // Listen for connectivity changes to refresh emergency cache
    ConnectivityService().addOnlineCallback(() {
      // When back online, refresh emergency status cache
      EmergencyModeService.refreshCacheOnConnectivity();
    });
  }

  Future<List<String>?> _showGroupSelectionDialog() async {
    return await showDialog<List<String>>(
      context: context,
      builder: (context) => _GroupSelectionDialog(
        emergencyService: _emergencyService,
      ),
    );
  }

  Future<void> _initializeStreamService() async {
    try {
      print('📱 Initializing stream-based data service...');

      // Don't clear cache on initialization - keep it persistent across navigation
      // _ModernContactCardState.clearCache(); // REMOVED

      // Initialize the data stream service
      await _dataStreamService.initialize();

      // Subscribe to data streams
      _dataStreamService.contactsStream.listen((contacts) {
        if (mounted) {
          // Only clear cache if the contact list has actually changed significantly
          // AND we're not just initializing from empty state (navigation scenario)
          final isInitializing =
              _contactsUsingApp.isEmpty && contacts.isNotEmpty;
          final hasSignificantChange =
              _contactsUsingApp.length != contacts.length && !isInitializing;

          // Check if this is just an assignment update (same contacts, different assignments)
          final isAssignmentUpdate =
              _contactsUsingApp.length == contacts.length &&
                  _contactsUsingApp.isNotEmpty &&
                  contacts.isNotEmpty;

          if (hasSignificantChange) {
            print(
                '📱 Contact list significantly changed: ${_contactsUsingApp.length} -> ${contacts.length}');
            _ModernContactCardState.clearCache(forceRefresh: true);
          } else if (isInitializing) {
            print(
                '📱 Contact list initializing: ${_contactsUsingApp.length} -> ${contacts.length} (preserving cache)');
          } else if (isAssignmentUpdate) {
            print(
                '📱 Contact assignments updated: ${contacts.length} contacts (preserving UI cache)');
            // Don't clear cache for assignment updates - just update the contact list
          }

          setState(() {
            _contactsUsingApp = contacts;
            // Update filtered contacts based on current search
            if (_searchQuery.isEmpty) {
              _filteredContacts = contacts;
            } else {
              _filterContacts(_searchQuery);
            }
          });
        }
      });

      _dataStreamService.loadingStateStream.listen((loadingState) {
        if (mounted) {
          setState(() {
            _loadingState = loadingState;
          });
        }
      });

      _dataStreamService.errorStream.listen((error) {
        if (mounted) {
          setState(() {
            _error = error;
          });

          // Handle specific errors
          if (error != null) {
            if (error.contains('Bad state') || error.contains('disposed')) {
              // Show restart dialog for critical errors
              _showRestartDialog();
            }
          }
        }
      });

      // Load initial data (cache-first approach for navigation)
      await _dataStreamService.loadContactsData(forceRefresh: false);

      print('✅ Stream service initialized successfully');
    } catch (e) {
      print('❌ Failed to initialize stream service: $e');
      if (mounted) {
        setState(() {
          _loadingState = DataLoadingState.error;
          _error = 'Failed to initialize: $e';
        });
      }
    }
  }

  @override
  void dispose() {
    // Clean up search controllers
    _searchController.dispose();
    _searchFocusNode.dispose();

    // Don't dispose the singleton DataStreamService here
    // It will be managed by the app lifecycle
    super.dispose();
  }

  // Search functionality methods
  void _setupSearchListeners() {
    _searchController.addListener(() {
      final query = _searchController.text;
      if (query != _searchQuery) {
        setState(() {
          _searchQuery = query;
          _showSearchHistory =
              query.isEmpty && _isSearchActive && _searchHistory.isNotEmpty;
        });
        _filterContacts(query);
      }
    });

    _searchFocusNode.addListener(() {
      setState(() {
        _isSearchActive = _searchFocusNode.hasFocus;
        _showSearchHistory = _searchFocusNode.hasFocus &&
            _searchQuery.isEmpty &&
            _searchHistory.isNotEmpty;
      });

      // Debug print to see what's happening
      print(
          '🔍 Search focus changed: hasFocus=${_searchFocusNode.hasFocus}, query="$_searchQuery", historyCount=${_searchHistory.length}, showHistory=$_showSearchHistory');
    });
  }

  Future<void> _loadSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final history = prefs.getStringList('contacts_search_history') ?? [];
      setState(() {
        _searchHistory = history;
      });
      print('🔍 Loaded search history: $_searchHistory');
    } catch (e) {
      print('Error loading search history: $e');
    }
  }

  Future<void> _saveSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('contacts_search_history', _searchHistory);
    } catch (e) {
      print('Error saving search history: $e');
    }
  }

  void _addToSearchHistory(String query) {
    if (query.trim().isEmpty) return;

    final trimmedQuery = query.trim();
    setState(() {
      // Remove if already exists
      _searchHistory.remove(trimmedQuery);
      // Add to beginning
      _searchHistory.insert(0, trimmedQuery);
      // Keep only last 10 searches
      if (_searchHistory.length > 10) {
        _searchHistory = _searchHistory.take(10).toList();
      }
    });
    _saveSearchHistory();
  }

  void _clearSearchHistory() {
    setState(() {
      _searchHistory.clear();
    });
    _saveSearchHistory();
  }

  void _filterContacts(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredContacts = _contactsUsingApp;
      });
      return;
    }

    final lowercaseQuery = query.toLowerCase();
    setState(() {
      _filteredContacts = _contactsUsingApp.where((contact) {
        final name = contact.displayName.toLowerCase();
        final phone = contact.profile?.phoneNumber?.toLowerCase() ?? '';
        final primaryPhone = contact.deviceContact.phoneNumbers.isNotEmpty
            ? contact.deviceContact.phoneNumbers.first.toLowerCase()
            : '';

        return name.contains(lowercaseQuery) ||
            phone.contains(lowercaseQuery) ||
            primaryPhone.contains(lowercaseQuery);
      }).toList();
    });
  }

  void _performSearch(String query) {
    if (query.trim().isEmpty) return;

    print('🔍 Performing search for: "$query"');

    _addToSearchHistory(query);
    _searchController.text = query;
    _searchFocusNode.unfocus();
    setState(() {
      _showSearchHistory = false;
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = '';
      _filteredContacts = _contactsUsingApp;
      _showSearchHistory = false;
    });
  }

  Future<void> _refreshContacts() async {
    try {
      print('📱 Refreshing contacts data (preserving assignments)...');
      // Don't clear UI cache for refresh - preserve category assignments
      // Only refresh the underlying data
      await _dataStreamService.loadContactsData(forceRefresh: true);
    } catch (e) {
      print('❌ Error refreshing contacts: $e');
      if (mounted) {
        setState(() {
          _error = 'Failed to refresh contacts: $e';
        });
      }
    }
  }

  void _showRestartDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            AppLocalizations.of(context)?.connectionError ?? 'Connection Error',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          content: const Text(
            'A connection error occurred. Please try again or restart the app.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Try to restart the service
                _restartService();
              },
              child:
                  Text(AppLocalizations.of(context)?.tryAgain ?? 'Try Again'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Close the app (on Android)
                if (Navigator.canPop(context)) {
                  Navigator.of(context).pop();
                }
              },
              child: Text(AppLocalizations.of(context)?.close ?? 'Close'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _restartService() async {
    try {
      setState(() {
        _loadingState = DataLoadingState.loading;
        _error = null;
      });

      await _dataStreamService.restartService();

      setState(() {
        _loadingState = DataLoadingState.loaded;
      });
    } catch (e) {
      setState(() {
        _loadingState = DataLoadingState.error;
        _error = 'Failed to restart service: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(context),
              Expanded(child: _buildBody()),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 800),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              padding: const EdgeInsets.fromLTRB(24, 20, 24, 16),
              child: Column(
                children: [
                  // Title and refresh button row
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context)?.contacts ??
                                  'Contacts',
                              style: Theme.of(context)
                                  .textTheme
                                  .headlineMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                    fontSize: 32,
                                  ),
                            ),
                            // const SizedBox(height: 4),
                            // Text(
                            //   AppLocalizations.of(context)?.peopleUsingContactTimes ?? 'People using Contact Times',
                            //   style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            //     color: Colors.white.withOpacity(0.8),
                            //     fontSize: 16,
                            //   ),
                            // ),
                          ],
                        ),
                      ),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: IconButton(
                          icon: const Icon(
                            Icons.refresh_rounded,
                            color: Colors.white,
                          ),
                          onPressed: _refreshContacts,
                          tooltip:
                              AppLocalizations.of(context)?.refreshContacts ??
                                  'Refresh contacts',
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Emergency button
                  _buildEmergencyButton(context),
                  const SizedBox(height: 16),
                  // Search bar
                  _buildSearchBar(context),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmergencyButton(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: _isEmergencyMode
                ? [
                    const Color(0xFFFF6B6B), // Red
                    const Color(0xFFFF8E8E), // Light red
                  ]
                : [
                    Colors.white.withOpacity(0.2),
                    Colors.white.withOpacity(0.1),
                  ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: _isEmergencyMode
                ? Colors.white.withOpacity(0.6)
                : Colors.white.withOpacity(0.3),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: _isEmergencyMode
                  ? const Color(0xFFFF6B6B).withOpacity(0.3)
                  : Colors.black.withOpacity(0.1),
              blurRadius: _isEmergencyMode ? 15 : 10,
              offset: const Offset(0, 4),
              spreadRadius: _isEmergencyMode ? 2 : 0,
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(20),
            onTap: _isEmergencyMode
                ? _showEmergencyStatusDialog
                : _showEmergencyActivationDialog,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: Row(
                children: [
                  // Emergency icon with animation
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _isEmergencyMode
                          ? Colors.white.withOpacity(0.2)
                          : Colors.white.withOpacity(0.15),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _isEmergencyMode
                          ? Icons.emergency
                          : Icons.shield_outlined,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Emergency text
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _isEmergencyMode
                              ? (AppLocalizations.of(context)
                                      ?.emergencyModeActive ??
                                  'Emergency Mode Active')
                              : (AppLocalizations.of(context)?.emergencyMode ??
                                  'Emergency Mode'),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (_isEmergencyMode) ...[
                          const SizedBox(height: 2),
                          Text(
                            _emergencyService
                                .getLocalizedFormattedRemainingTime(context),
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.9),
                              fontSize: 12,
                            ),
                          ),
                        ] else
                          Text(
                            AppLocalizations.of(context)
                                    ?.tapToActivateEmergencyMode ??
                                'Tap to activate emergency mode',
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.8),
                              fontSize: 12,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Arrow or status icon
                  Icon(
                    _isEmergencyMode ? Icons.settings : Icons.arrow_forward_ios,
                    color: Colors.white.withOpacity(0.8),
                    size: 16,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Show emergency activation dialog with duration selection
  Future<void> _showEmergencyActivationDialog() async {
    final selectedDuration = await showDialog<EmergencyDuration>(
      context: context,
      builder: (context) => _EmergencyActivationDialog(),
    );

    if (selectedDuration != null) {
      // Show group selection dialog before activating emergency mode
      final selectedGroups = await _showGroupSelectionDialog();

      await _emergencyService.activateEmergencyMode(
        selectedDuration,
        exceptionGroups: selectedGroups,
      );
    }
  }

  // Show emergency status dialog (when already active)
  Future<void> _showEmergencyStatusDialog() async {
    await showDialog(
      context: context,
      builder: (context) => _EmergencyStatusDialog(
        emergencyService: _emergencyService,
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.15),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: TextField(
            controller: _searchController,
            focusNode: _searchFocusNode,
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.search,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
            decoration: InputDecoration(
              hintText: AppLocalizations.of(context)?.searchContacts ??
                  'Search contacts...',
              hintStyle: TextStyle(
                color: Colors.white.withOpacity(0.6),
                fontSize: 16,
              ),
              prefixIcon: Icon(
                Icons.search_rounded,
                color: Colors.white.withOpacity(0.8),
                size: 24,
              ),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: Icon(
                        Icons.clear_rounded,
                        color: Colors.white.withOpacity(0.8),
                        size: 20,
                      ),
                      onPressed: _clearSearch,
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 16,
              ),
            ),
            onSubmitted: (query) {
              if (query.trim().isNotEmpty) {
                _performSearch(query.trim());
              }
            },
          ),
        ),
        // Search history overlay
        if (_showSearchHistory && _searchHistory.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 8),
            child: _buildSearchHistory(context),
          ),
      ],
    );
  }

  Widget _buildSearchHistory(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.fromLTRB(20, 16, 16, 8),
            child: Row(
              children: [
                Icon(
                  Icons.history_rounded,
                  color: Colors.grey[600],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    AppLocalizations.of(context)?.recentSearches ??
                        'Recent searches',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
                TextButton(
                  onPressed: _clearSearchHistory,
                  style: TextButton.styleFrom(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  child: Text(
                    AppLocalizations.of(context)?.clear ?? 'Clear',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // History items
          ...(_searchHistory
              .take(5)
              .map((query) => _buildSearchHistoryItem(context, query))),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  Widget _buildSearchHistoryItem(BuildContext context, String query) {
    return InkWell(
      onTap: () => _performSearch(query),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        child: Row(
          children: [
            Icon(
              Icons.history_rounded,
              color: Colors.grey[400],
              size: 18,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                query,
                style: TextStyle(
                  fontSize: 15,
                  color: Colors.grey[800],
                ),
              ),
            ),
            Icon(
              Icons.north_west_rounded,
              color: Colors.grey[400],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  String _buildContactsCountText(BuildContext context) {
    if (_searchQuery.isEmpty) {
      final count = _contactsUsingApp.length;
      return AppLocalizations.of(context)
              ?.contactsCount(count, count == 1 ? '' : 's') ??
          '$count contact${count == 1 ? '' : 's'}';
    } else {
      final filteredCount = _filteredContacts.length;
      final totalCount = _contactsUsingApp.length;
      return AppLocalizations.of(context)
              ?.searchResults(filteredCount, totalCount) ??
          '$filteredCount of $totalCount contacts';
    }
  }

  Widget _buildBody() {
    if (_loadingState == DataLoadingState.loading ||
        _loadingState == DataLoadingState.initial) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(32),
              margin: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.15),
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 60,
                    height: 60,
                    child: CircularProgressIndicator(
                      strokeWidth: 4,
                      valueColor:
                          const AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    AppLocalizations.of(context)?.loadingContacts ??
                        'Loading contacts...',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Container(
          margin: const EdgeInsets.all(24),
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.15),
            borderRadius: BorderRadius.circular(24),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: _error!.contains('permission')
                      ? Colors.orange.withOpacity(0.2)
                      : Colors.red.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: _error!.contains('permission')
                        ? Colors.orange.withOpacity(0.3)
                        : Colors.red.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Icon(
                  _error!.contains('permission')
                      ? Icons.contacts_rounded
                      : Icons.error_outline_rounded,
                  size: 48,
                  color: _error!.contains('permission')
                      ? Colors.orange[300]
                      : Colors.red[300],
                ),
              ),
              const SizedBox(height: 24),
              Text(
                _error!.contains('permission')
                    ? (AppLocalizations.of(context)?.permissionRequired ??
                        'Permission Required')
                    : (AppLocalizations.of(context)?.somethingWentWrong ??
                        'Something went wrong'),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                _error!,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white.withOpacity(0.8),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              if (_error!.contains('permission')) ...[
                _buildModernButton(
                  onPressed: () async {
                    await ContactsService.openAppSettings();
                  },
                  text: AppLocalizations.of(context)?.openSettings ??
                      'Open Settings',
                  isPrimary: true,
                  icon: Icons.settings_rounded,
                ),
                const SizedBox(height: 12),
              ],
              _buildModernButton(
                onPressed: _refreshContacts,
                text: AppLocalizations.of(context)?.tryAgain ?? 'Try Again',
                isPrimary: !_error!.contains('permission'),
                icon: Icons.refresh_rounded,
              ),
            ],
          ),
        ),
      );
    }

    // Check for empty states
    final contactsList =
        _searchQuery.isEmpty ? _contactsUsingApp : _filteredContacts;

    if (contactsList.isEmpty) {
      return Center(
        child: Container(
          margin: const EdgeInsets.all(24),
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.15),
            borderRadius: BorderRadius.circular(24),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Icon(
                  _searchQuery.isEmpty
                      ? Icons.people_outline_rounded
                      : Icons.search_off_rounded,
                  size: 48,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                _searchQuery.isEmpty
                    ? (AppLocalizations.of(context)?.noContactsUsingApp ??
                        'No contacts using the app')
                    : (AppLocalizations.of(context)?.noSearchResults ??
                        'No search results'),
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                _searchQuery.isEmpty
                    ? (AppLocalizations.of(context)
                            ?.noContactsUsingAppDescription ??
                        'None of your contacts are using Contact Times yet.\n\nTo see contacts here:\n• Invite friends to download the app\n• Make sure they sign up with their phone numbers\n• Their numbers should be in your device contacts')
                    : (AppLocalizations.of(context)
                            ?.noSearchResultsDescription(_searchQuery) ??
                        'No contacts found matching "$_searchQuery".\n\nTry:\n• Checking the spelling\n• Using a different search term\n• Searching by name or phone number'),
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white.withOpacity(0.8),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              if (_searchQuery.isNotEmpty) ...[
                _buildModernButton(
                  onPressed: _clearSearch,
                  text: AppLocalizations.of(context)?.clearSearch ??
                      'Clear search',
                  isPrimary: true,
                  icon: Icons.clear_rounded,
                ),
              ] else ...[
                _buildModernButton(
                  onPressed: _refreshContacts,
                  text: AppLocalizations.of(context)?.refresh ?? 'Refresh',
                  isPrimary: true,
                  icon: Icons.refresh_rounded,
                ),
              ],
            ],
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(bottom: 20),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.people_rounded,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  _buildContactsCountText(context),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              physics: const BouncingScrollPhysics(),
              itemCount:
                  (_searchQuery.isEmpty ? _contactsUsingApp : _filteredContacts)
                      .length,
              itemBuilder: (context, index) {
                final contactsList = _searchQuery.isEmpty
                    ? _contactsUsingApp
                    : _filteredContacts;
                final contact = contactsList[index];
                return TweenAnimationBuilder<double>(
                  duration: Duration(milliseconds: 300 + (index * 100)),
                  tween: Tween(begin: 0.0, end: 1.0),
                  curve: Curves.easeOutCubic,
                  builder: (context, value, child) {
                    return Transform.translate(
                      offset: Offset(0, 30 * (1 - value)),
                      child: Opacity(
                        opacity: value,
                        child: ModernContactCard(
                          contact: contact,
                          onTap: () => _showContactDetail(contact),
                          index: index,
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernButton({
    required VoidCallback onPressed,
    required String text,
    required bool isPrimary,
    required IconData icon,
  }) {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        color: isPrimary
            ? Colors.white.withOpacity(0.2)
            : Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(isPrimary ? 0.3 : 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                text,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showContactDetail(ContactWithProfile contact) async {
    // Check if contact has app account
    if (!contact.hasAppAccount) {
      // Show dialog for non-app users
      _showNonAppUserDialog(contact);
      return;
    }

    // Get current user profile
    final currentProfile = await OfflineContactService().getCurrentProfile();
    if (currentProfile == null) return;

    // Clear cache and check if the CURRENT USER is in emergency mode (force fresh check)
    EmergencyModeService.clearCacheForUser(currentProfile.id);
    EmergencyStatus? currentUserEmergencyStatus;

    try {
      currentUserEmergencyStatus =
          await EmergencyModeService.checkUserEmergencyStatus(
              currentProfile.id);
    } catch (e) {
      print('⚠️ Current user emergency status check failed: $e');
      // If emergency check fails, assume user is NOT in emergency mode
      currentUserEmergencyStatus = null;
    }

    print('🔍 Contact Detail Check - Current User ID: ${currentProfile.id}');
    print(
        '🔍 Current User Emergency Status: ${currentUserEmergencyStatus?.isActive ?? false}');

    // If current user is in emergency mode, allow them to contact others freely
    if (currentUserEmergencyStatus != null &&
        currentUserEmergencyStatus.isActive) {
      // Current user is in emergency mode - allow them to contact others without restrictions
      print(
          '🚨 Current user is in emergency mode - allowing free contact access');
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ContactDetailScreen(contact: contact),
          ),
        );
      }
      return;
    }

    // Check if the CONTACT (person being viewed) is in emergency mode
    EmergencyStatus? contactEmergencyStatus;

    try {
      contactEmergencyStatus =
          await EmergencyModeService.checkUserEmergencyStatus(
              contact.profile!.id);
    } catch (e) {
      print('⚠️ Contact emergency status check failed: $e');
      // If emergency check fails, assume contact is NOT in emergency mode
      contactEmergencyStatus = null;
    }

    // 🧪 TEMPORARY TEST: Simulate emergency mode for specific contact
    // TODO: Remove this test code once database schema is fixed
    if (contact.profile?.fullName == 'mam') {
      print(
          '🧪 TEST: Simulating emergency mode for contact detail: ${contact.profile?.fullName}');
      contactEmergencyStatus = EmergencyStatus(
        isActive: true,
        startTime: DateTime.now().subtract(Duration(minutes: 30)),
        expiryTime: DateTime.now().add(Duration(hours: 2)),
        exceptionGroups: ['family', 'co-workers'],
        userName: contact.profile?.fullName ?? 'Test User',
      );
    }

    if (contactEmergencyStatus != null && contactEmergencyStatus.isActive) {
      if (mounted) {
        await _showContactEmergencyDialog(
            context, contact, contactEmergencyStatus);
      }
      return;
    }

    if (mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ContactDetailScreen(contact: contact),
        ),
      );
    }
  }

  void _showNonAppUserDialog(ContactWithProfile contact) {
    showDialog(
      context: context,
      builder: (context) => _NonAppUserDialog(contact: contact),
    );
  }

  Future<void> _showContactEmergencyDialog(BuildContext context,
      ContactWithProfile contact, EmergencyStatus emergencyStatus) async {
    await showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(32),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 40),
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFFFF6B6B), // Red
                Color(0xFFFF8E8E), // Light red
                Color(0xFFFFB3B3), // Very light red
              ],
              stops: [0.0, 0.6, 1.0],
            ),
            borderRadius: BorderRadius.circular(32),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFFF6B6B).withOpacity(0.4),
                blurRadius: 30,
                offset: const Offset(0, 15),
                spreadRadius: 5,
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(32),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(28),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Emergency icon with animation
                  TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 1500),
                    tween: Tween(begin: 0.8, end: 1.0),
                    curve: Curves.easeInOut,
                    builder: (context, scale, child) {
                      return Transform.scale(
                        scale: scale,
                        child: Container(
                          width: 90,
                          height: 90,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.25),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white.withOpacity(0.3),
                              width: 2,
                            ),
                          ),
                          child: const Icon(
                            Icons.emergency,
                            size: 45,
                            color: Colors.white,
                          ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 24),

                  // Title
                  Text(
                    AppLocalizations.of(context)?.emergencyModeActive ??
                        'Emergency Mode Active',
                    style: const TextStyle(
                      fontSize: 26,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      letterSpacing: -0.5,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 16),

                  // Contact name
                  Text(
                    AppLocalizations.of(context)
                            ?.contactNotAvailable(contact.displayName) ??
                        '${contact.displayName} is not available',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 20),

                  // Emergency message
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        // Main emergency message
                        Text(
                          '${emergencyStatus.userName} في وضع الطوارئ حالياً. غير متاح للمكالمات أو الرسائل.',
                          style: TextStyle(
                            fontSize: 15,
                            color: Colors.white.withOpacity(0.95),
                            height: 1.5,
                            fontWeight: FontWeight.w400,
                          ),
                          textAlign: TextAlign.center,
                        ),

                        // Exception groups (highlighted)
                        if (emergencyStatus.exceptionGroups.isNotEmpty) ...[
                          const SizedBox(height: 16),
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.orange.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.orange.withOpacity(0.4),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.group,
                                  color: Colors.orange.shade300,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'المجموعات المستثناة: ${emergencyStatus.exceptionGroups.map((group) => EmergencyModeService.getLocalizedGroupName(group, AppLocalizations.of(context))).join('، ')}',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.orange.shade200,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],

                        // Remaining time
                        const SizedBox(height: 16),
                        Text(
                          'وضع الطوارئ: ${EmergencyModeService.getFormattedRemainingTimeForUser(emergencyStatus, AppLocalizations.of(context))}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white.withOpacity(0.8),
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Close button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white.withOpacity(0.95),
                        foregroundColor: const Color(0xFFFF6B6B),
                        padding: const EdgeInsets.symmetric(vertical: 18),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        elevation: 0,
                        shadowColor: Colors.transparent,
                      ),
                      child: Text(
                        AppLocalizations.of(context)?.understood ??
                            'Understood',
                        style: const TextStyle(
                          fontSize: 17,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// ContactWithProfile class is now defined in data_stream_service.dart

class ModernContactCard extends StatefulWidget {
  final ContactWithProfile contact;
  final VoidCallback onTap;
  final int index;

  const ModernContactCard({
    super.key,
    required this.contact,
    required this.onTap,
    required this.index,
  });

  @override
  State<ModernContactCard> createState() => _ModernContactCardState();

  // Static helper for dialog gradient colors
  static List<Color> getDialogGradientColors(Category? category) {
    if (category == null) {
      // Default app theme colors when no category
      return [
        const Color(0xFF6366F1).withOpacity(0.1), // Indigo
        const Color(0xFF8B5CF6).withOpacity(0.1), // Purple
      ];
    }

    // Get category colors and create light gradient
    final categoryColor = category.type.getColor();
    return [
      categoryColor.withOpacity(0.1),
      categoryColor.withOpacity(0.2),
    ];
  }
}

class _ModernContactCardState extends State<ModernContactCard> {
  Category? _assignedCategory;
  bool _isLoadingCategory = true;

  // Use singleton services to avoid creating new instances
  final DataStreamService _dataStreamService = DataStreamService();
  final OfflineContactService _offlineService = OfflineContactService();

  // Static cache to prevent reloading during scrolling
  static final Map<String, Category?> _categoryCache = {};
  static final Map<String, bool> _loadingCache = {};

  // Cache key for this contact
  late String _contactCacheKey;

  // Stream subscription for real-time updates
  StreamSubscription<Map<String, Category?>>? _assignmentsSubscription;

  @override
  void initState() {
    super.initState();
    _contactCacheKey = _generateCacheKey();
    _loadAssignedCategoryFromCache();
    _subscribeToAssignmentUpdates();
    _listenForCacheUpdates();
  }

  String _generateCacheKey() {
    final phoneNumber = widget.contact.profile?.phoneNumber ??
        widget.contact.primaryPhoneNumber;
    final cleanedPhone = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    return 'contact_category_$cleanedPhone';
  }

  void _subscribeToAssignmentUpdates() {
    // Subscribe to real-time assignment updates
    _assignmentsSubscription =
        _offlineService.contactAssignmentsStream.listen((assignments) {
      // Check if this contact's assignment has changed
      final phoneNumber = widget.contact.profile?.phoneNumber ??
          widget.contact.primaryPhoneNumber;
      final cleanedPhone = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
      final phoneVariations =
          ContactsService.generatePhoneVariations(phoneNumber);

      Category? newCategory;

      // Check all phone variations for assignment
      for (final variation in phoneVariations) {
        if (assignments.containsKey(variation)) {
          newCategory = assignments[variation];
          break;
        }
      }

      // Also check the cleaned phone directly
      if (newCategory == null && assignments.containsKey(cleanedPhone)) {
        newCategory = assignments[cleanedPhone];
      }

      // Update cache and UI if assignment changed
      if (_categoryCache[_contactCacheKey] != newCategory) {
        _categoryCache[_contactCacheKey] = newCategory;

        // Update SharedPreferences cache immediately
        _updateSharedPreferencesCache(cleanedPhone, newCategory);

        if (mounted) {
          setState(() {
            _assignedCategory = newCategory;
            _isLoadingCategory = false;
          });
        }

        print(
            '📱 Real-time update: Contact ${widget.contact.displayName} assignment changed to ${newCategory?.type.name ?? 'none'}');
      }
    });
  }

  // Update SharedPreferences cache for this contact
  Future<void> _updateSharedPreferencesCache(
      String contactPhone, Category? category) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final contactCacheKey = 'contact_category_$contactPhone';

      if (category != null) {
        final categoryData = {
          'id': category.id,
          'type': category.type.index,
          'note': category.note,
          'timeSlots': category.timeSlots
              .map((slot) => {
                    'id': slot.id,
                    'dayOfWeek': slot.dayOfWeek,
                    'startTime': slot.startTime,
                    'endTime': slot.endTime,
                  })
              .toList(),
        };

        await prefs.setString(contactCacheKey, jsonEncode(categoryData));
      } else {
        await prefs.remove(contactCacheKey);
      }

      // Update cache timestamp to notify other parts of the app
      await prefs.setString(
          'contacts_cache_updated', DateTime.now().toIso8601String());

      print('📱 Updated SharedPreferences cache for contact $contactPhone');
    } catch (e) {
      print('❌ Error updating SharedPreferences cache: $e');
    }
  }

  Future<void> _loadAssignedCategoryFromCache() async {
    try {
      final currentUser = SupabaseService.currentUser;
      if (currentUser == null) {
        setState(() => _isLoadingCategory = false);
        return;
      }

      // Check if already cached in static cache
      if (_categoryCache.containsKey(_contactCacheKey)) {
        setState(() {
          _assignedCategory = _categoryCache[_contactCacheKey];
          _isLoadingCategory = false;
        });
        return;
      }

      // Try SharedPreferences cache next
      final phoneNumber = widget.contact.profile?.phoneNumber ??
          widget.contact.primaryPhoneNumber;
      final cleanedPhone = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
      final sharedPrefsCategory =
          await _loadFromSharedPreferencesCache(cleanedPhone);

      if (sharedPrefsCategory != null) {
        _categoryCache[_contactCacheKey] = sharedPrefsCategory;
        setState(() {
          _assignedCategory = sharedPrefsCategory;
          _isLoadingCategory = false;
        });
        print(
            '📱 Using SharedPreferences cache for ${widget.contact.displayName}: ${sharedPrefsCategory.type.name}');
        return;
      }

      // Check if already loading to prevent duplicate requests
      if (_loadingCache[_contactCacheKey] == true) {
        // Wait for the other request to complete
        while (_loadingCache[_contactCacheKey] == true) {
          await Future.delayed(const Duration(milliseconds: 50));
        }
        if (_categoryCache.containsKey(_contactCacheKey)) {
          setState(() {
            _assignedCategory = _categoryCache[_contactCacheKey];
            _isLoadingCategory = false;
          });
        }
        return;
      }

      // Mark as loading
      _loadingCache[_contactCacheKey] = true;

      // Use the optimized cache-first method from OfflineContactService
      Category? category = await _offlineService.getCategoryAssignedToContact(
        currentUserId: currentUser.id,
        contactPhone: cleanedPhone,
      );

      // If not found with cleaned phone, try with original phone number
      if (category == null) {
        category = await _offlineService.getCategoryAssignedToContact(
          currentUserId: currentUser.id,
          contactPhone: phoneNumber,
        );
      }

      // Cache the result in static cache for UI performance
      _categoryCache[_contactCacheKey] = category;
      _loadingCache[_contactCacheKey] = false;

      if (mounted) {
        setState(() {
          _assignedCategory = category;
          _isLoadingCategory = false;
        });
      }
    } catch (e) {
      print('❌ Error loading assigned category from cache: $e');
      _loadingCache[_contactCacheKey] = false;
      if (mounted) {
        setState(() {
          _isLoadingCategory = false;
        });
      }
    }
  }

  // Listen for cache updates from connectivity restoration
  void _listenForCacheUpdates() {
    // Check for cache updates periodically when app is active
    Timer.periodic(const Duration(seconds: 5), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      _checkForCacheUpdates();
    });
  }

  // Check if cache has been updated and refresh if needed
  void _checkForCacheUpdates() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastCacheUpdate = prefs.getString('contacts_cache_updated');

      if (lastCacheUpdate != null) {
        final cacheUpdateTime = DateTime.parse(lastCacheUpdate);
        final now = DateTime.now();

        // If cache was updated in the last 30 seconds, refresh the contact data
        if (now.difference(cacheUpdateTime).inSeconds < 30) {
          print('📱 Cache recently updated, refreshing contact data');
          await _loadAssignedCategoryFromCache();
        }
      }
    } catch (e) {
      print('❌ Error checking cache updates: $e');
    }
  }

  // Load category from SharedPreferences cache
  Future<Category?> _loadFromSharedPreferencesCache(String contactPhone) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final contactCacheKey = 'contact_category_$contactPhone';
      final cachedData = prefs.getString(contactCacheKey);

      if (cachedData != null) {
        final categoryData = jsonDecode(cachedData) as Map<String, dynamic>;

        // Reconstruct category from cached data
        final timeSlots = (categoryData['timeSlots'] as List<dynamic>?)
                ?.map((slotData) => TimeSlot(
                      id: slotData['id'] as String,
                      categoryId: categoryData['id'] as String,
                      dayOfWeek: slotData['dayOfWeek'] as int,
                      startTime: CustomTimeOfDay.fromString(
                          slotData['startTime'] as String),
                      endTime: CustomTimeOfDay.fromString(
                          slotData['endTime'] as String),
                      createdAt: DateTime.now(), // Placeholder
                    ))
                .toList() ??
            [];

        return Category(
          id: categoryData['id'] as String,
          userId: '', // Will be filled by service
          type: CategoryType.values[categoryData['type'] as int],
          note: categoryData['note'] as String,
          timeSlots: timeSlots,
          createdAt: DateTime.now(), // Placeholder
          updatedAt: DateTime.now(), // Placeholder
        );
      }
    } catch (e) {
      print('❌ Error loading from SharedPreferences cache: $e');
    }
    return null;
  }

  // Static method to clear cache when needed (only for force refresh)
  static void clearCache({bool forceRefresh = false}) {
    if (forceRefresh) {
      print('📱 Force clearing all contact category cache');
      _categoryCache.clear();
      _loadingCache.clear();
    } else {
      print('📱 Skipping cache clear - preserving cache for navigation');
    }
  }

  // Static method to update cache for a specific contact
  static void updateCacheForContact(String phoneNumber, Category? category) {
    final cleanedPhone = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    final cacheKey = 'contact_category_$cleanedPhone';
    _categoryCache[cacheKey] = category;
    print(
        '📱 Updated cache for contact $phoneNumber: ${category?.type.name ?? 'none'}');
  }

  // Static method to clear cache for a specific contact
  static void clearCacheForContact(String phoneNumber) {
    final cleanedPhone = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    final cacheKey = 'contact_category_$cleanedPhone';
    _categoryCache.remove(cacheKey);
    _loadingCache.remove(cacheKey);
    print('📱 Cleared cache for contact $phoneNumber');
  }

  void _showNonAppUserDialogFromCard(
      BuildContext context, ContactWithProfile contact) {
    // Check if current user is in emergency mode
    // final emergencyService = EmergencyModeService();
    // if (emergencyService.isEmergencyMode) {
    //   _showEmergencyModeDialog(context, contact, emergencyService);
    //   return;
    // }

    showDialog(
      context: context,
      builder: (context) => _NonAppUserDialog(contact: contact),
    );
  }

  @override
  void dispose() {
    // Cancel stream subscription
    _assignmentsSubscription?.cancel();

    // Don't clear the entire cache on dispose, just mark this contact as not loading
    _loadingCache[_contactCacheKey] = false;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.onTap,
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                _buildAvatar(),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.contact.displayName,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[800],
                        ),
                      ),
                      const SizedBox(height: 4),
                      LTROverride(
                        child: Text(
                          ContactsService.formatPhoneNumber(
                              widget.contact.profile?.phoneNumber ??
                                  widget.contact.primaryPhoneNumber),
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildCategoryChip(),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                _buildActionButtons(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    final colors = [
      Colors.blue,
      Colors.purple,
      Colors.green,
      Colors.orange,
      Colors.pink,
      Colors.teal,
    ];
    final color = colors[widget.index % colors.length];

    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [color.shade400, color.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(18),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: widget.contact.deviceContact.avatar != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(18),
              child: _buildAsyncAvatar(color),
            )
          : Center(
              child: Text(
                widget.contact.displayName.isNotEmpty
                    ? widget.contact.displayName[0].toUpperCase()
                    : '?',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
    );
  }

  /// Build avatar image asynchronously to prevent UI freezing
  Widget _buildAsyncAvatar(Color fallbackColor) {
    try {
      final avatarData = widget.contact.deviceContact.avatar;
      if (avatarData != null && avatarData.isNotEmpty) {
        // Try to decode the avatar data safely
        return FutureBuilder<Uint8List?>(
          future: _decodeAvatarSafely(avatarData),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.done) {
              if (snapshot.hasData && snapshot.data != null) {
                return Image.memory(
                  snapshot.data!,
                  width: 56,
                  height: 56,
                  fit: BoxFit.cover,
                  gaplessPlayback: true,
                  errorBuilder: (context, error, stackTrace) {
                    print('Error displaying avatar image: $error');
                    return _buildFallbackAvatar(fallbackColor);
                  },
                );
              } else {
                // Failed to decode, show fallback
                return _buildFallbackAvatar(fallbackColor);
              }
            } else {
              // Still loading, show fallback
              return _buildFallbackAvatar(fallbackColor);
            }
          },
        );
      }
    } catch (e) {
      print('Error loading avatar for ${widget.contact.displayName}: $e');
    }
    // Return fallback if no avatar or error
    return _buildFallbackAvatar(fallbackColor);
  }

  /// Safely decode avatar data from various formats
  Future<Uint8List?> _decodeAvatarSafely(String avatarData) async {
    try {
      // Check if it's base64 encoded
      if (avatarData.startsWith('data:image/')) {
        // Remove data URL prefix if present
        final base64String = avatarData.split(',').last;
        return base64Decode(base64String);
      } else if (avatarData.length > 100 && !avatarData.contains('/')) {
        // Likely a base64 string without prefix
        return base64Decode(avatarData);
      } else {
        // Might be a file path or URL - not supported for now
        print('Unsupported avatar format: ${avatarData.substring(0, 50)}...');
        return null;
      }
    } catch (e) {
      print('Failed to decode avatar data: $e');
      return null;
    }
  }

  /// Build fallback avatar with initials
  Widget _buildFallbackAvatar(Color color) {
    return Center(
      child: Text(
        widget.contact.displayName.isNotEmpty
            ? widget.contact.displayName[0].toUpperCase()
            : '?',
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildCategoryChip() {
    if (_isLoadingCategory) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(width: 8),
            Text(
              AppLocalizations.of(context)?.loadingEllipsis ?? 'Loading...',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    if (_assignedCategory == null) {
      return GestureDetector(
        onTap: () => _showCategorySelectionDialog(),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.grey[300]!,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.add_circle_outline,
                size: 14,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 6),
              Text(
                AppLocalizations.of(context)?.assignCategory ??
                    'Assign Category',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Show assigned category with modern design
    final categoryColor = _getCategoryColor(_assignedCategory!.type);
    return GestureDetector(
      onTap: () => _showCategorySelectionDialog(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: categoryColor.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: categoryColor.withOpacity(0.4),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: categoryColor,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                _assignedCategory!.type.getDisplayName(context),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: categoryColor,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.edit,
              size: 12,
              color: categoryColor.withOpacity(0.8),
            ),
          ],
        ),
      ),
    );
  }

  Color _getCategoryColor(CategoryType type) {
    switch (type) {
      case CategoryType.contactAnytime:
        return Colors.green;
      case CategoryType.preferAnytime:
        return Colors.blue;
      case CategoryType.contactAtTimes:
        return Colors.orange;
      case CategoryType.contactThroughMessages:
        return Colors.purple;
    }
  }

  Future<void> _showCategorySelectionDialog() async {
    try {
      // Check if online first - category assignment requires internet connection
      final connectivityService = ConnectivityService();
      if (!connectivityService.isOnline) {
        await _showOfflineAssignmentDialog();
        return;
      }

      final currentUser = SupabaseService.currentUser;
      if (currentUser == null) {
        print('❌ No current user found for category selection');
        return;
      }

      print('📱 Loading categories for category selection dialog...');

      // Ensure offline service is initialized
      if (!_offlineService.isInitialized) {
        print('📱 Offline service not initialized, initializing now...');
        await _offlineService.initialize();
      }

      // Get user's categories from offline service (singleton instance)
      final categories = await _offlineService.getUserCategories();

      print('📱 Loaded ${categories.length} categories for dialog');
      for (final category in categories) {
        print('📱   - ${category.type.name} (ID: ${category.id})');
      }

      if (!mounted) return;

      // Check if categories are empty and show appropriate message
      if (categories.isEmpty) {
        print('❌ No categories found, showing error dialog');
        await _showNoCategoriesDialog();
        return;
      }

      final selectedCategory = await showDialog<Category>(
        context: context,
        builder: (context) => _CategorySelectionDialog(
          categories: categories,
          currentCategory: _assignedCategory,
          contactName: widget.contact.displayName,
        ),
      );

      if (selectedCategory != null) {
        await _assignCategory(selectedCategory);
      }
    } catch (e) {
      print('❌ Error showing category selection dialog: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)
                    ?.errorLoadingCategories(e.toString()) ??
                'Error loading categories: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  Future<void> _showNoCategoriesDialog() async {
    if (!mounted) return;

    await showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.warning_outlined,
                color: Colors.orange,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'No Categories Found',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        content: const Text(
          'No categories are available for assignment. Please check your internet connection and try again, or go to the Categories page to create categories.',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _showOfflineAssignmentDialog() async {
    if (!mounted) return;

    await showDialog<void>(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(24),
        ),
        backgroundColor: Colors.transparent,
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF6366F1), // Indigo
                const Color(0xFF8B5CF6), // Purple
                const Color(0xFF3B82F6), // Blue
              ],
            ),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.wifi_off_rounded,
                    size: 40,
                    color: Colors.white,
                  ),
                ),

                const SizedBox(height: 20),

                // Title
                Text(
                  AppLocalizations.of(context)?.noInternetConnection ??
                      'Internet Connection Required',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 12),

                // Message
                Text(
                  AppLocalizations.of(context)?.noInternetMessage ??
                      'Category assignment requires an internet connection to sync with other users. Please connect to the internet and try again.',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.9),
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 24),

                // Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor:
                          const Color(0xFF6366F1), // Indigo to match app theme
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      AppLocalizations.of(context)?.ok ?? 'OK',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Get dialog gradient colors based on assigned category
  List<Color> _getDialogGradientColors(Category? category) {
    return ModernContactCard.getDialogGradientColors(category);
  }

  Future<void> _assignCategory(Category category) async {
    try {
      final currentUser = SupabaseService.currentUser;
      if (currentUser == null) return;

      // Use the same phone number format as the Distribute tab (digits only)
      final phoneNumber = widget.contact.profile?.phoneNumber ??
          widget.contact.primaryPhoneNumber;
      final cleanedPhone = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

      print('🔄 ASSIGNING CATEGORY:');
      print('🔄   Contact: ${widget.contact.displayName}');
      print('🔄   Original phone: $phoneNumber');
      print('🔄   Cleaned phone: $cleanedPhone');
      print('🔄   User ID: ${currentUser.id}');
      print('🔄   Category: ${category.type.name} (ID: ${category.id})');

      // Update UI immediately for instant feedback (do this first for fastest response)
      if (mounted) {
        setState(() {
          _assignedCategory = category;
        });
      }

      // Update cache immediately for this specific contact with multiple keys
      final cacheKeys = [
        _contactCacheKey,
        'contact_category_$cleanedPhone',
        'contact_category_$phoneNumber',
      ];

      for (final key in cacheKeys) {
        _categoryCache[key] = category;
      }

      // Update offline service cache immediately for instant UI updates
      _offlineService.updateContactAssignment(cleanedPhone, category);

      // Also update with original phone number
      if (phoneNumber != cleanedPhone) {
        _offlineService.updateContactAssignment(phoneNumber, category);
      }

      // Show success message immediately
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)
                    ?.contactAssignedToCategory(widget.contact.displayName,
                        category.type.getDisplayName(context)) ??
                '${widget.contact.displayName} assigned to "${category.type.getDisplayName(context)}"'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }

      // Perform network assignment in background (don't await)
      _performBackgroundAssignment(currentUser.id, cleanedPhone, category.id);

      print('✅ Assignment UI updated immediately!');
    } catch (e) {
      print('❌ Error in _assignCategory: $e');

      // Revert UI change on error
      if (mounted) {
        setState(() {
          _assignedCategory = null; // Reset to previous state
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)
                    ?.errorAssigningCategory(e.toString()) ??
                'Error assigning category: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Perform the actual network assignment in the background
  void _performBackgroundAssignment(
      String userId, String contactPhone, String categoryId) async {
    try {
      await _dataStreamService.assignContactToCategory(
        userId: userId,
        contactPhone: contactPhone,
        categoryId: categoryId,
      );
      print('✅ Background assignment completed successfully!');
    } catch (e) {
      print('❌ Background assignment failed: $e');
      // The assignment will be retried when the app syncs or goes online
      // No need to show error to user since UI already updated optimistically
    }
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.green.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.green.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: IconButton(
            icon: const Icon(
              Icons.phone_rounded,
              color: Colors.green,
              size: 20,
            ),
            onPressed: () =>
                _initiateCall(context, widget.contact, _assignedCategory),
            tooltip: AppLocalizations.of(context)?.call ?? 'Call',
          ),
        ),
        const SizedBox(width: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.grey[300]!,
              width: 1,
            ),
          ),
          child: IconButton(
            icon: Icon(
              Icons.arrow_forward_ios_rounded,
              color: Colors.grey[600],
              size: 16,
            ),
            onPressed: widget.onTap,
            tooltip:
                AppLocalizations.of(context)?.viewDetails ?? 'View details',
          ),
        ),
      ],
    );
  }

  Future<void> _initiateCall(BuildContext context, ContactWithProfile contact,
      Category? assignedCategory) async {
    try {
      // Check if contact has app account
      if (!contact.hasAppAccount) {
        // Show dialog for non-app users
        _showNonAppUserDialogFromCard(context, contact);
        return;
      }

      // Get current user profile
      final currentProfile = await OfflineContactService().getCurrentProfile();
      if (currentProfile == null) return;

      // Clear cache and check if the CURRENT USER is in emergency mode (force fresh check)
      EmergencyModeService.clearCacheForUser(currentProfile.id);
      EmergencyStatus? currentUserEmergencyStatus;

      try {
        currentUserEmergencyStatus =
            await EmergencyModeService.checkUserEmergencyStatus(
                currentProfile.id);
      } catch (e) {
        print('⚠️ Current user emergency status check failed: $e');
        // If emergency check fails, assume user is NOT in emergency mode
        currentUserEmergencyStatus = null;
      }

      print('🔍 Phone Call Check - Current User ID: ${currentProfile.id}');
      print(
          '🔍 Current User Emergency Status: ${currentUserEmergencyStatus?.isActive ?? false}');

      // If current user is in emergency mode, allow them to make calls freely
      if (currentUserEmergencyStatus != null &&
          currentUserEmergencyStatus.isActive) {
        // Current user is in emergency mode - allow them to make calls without restrictions
        print(
            '🚨 Current user is in emergency mode - allowing free phone call');
        // Make the call directly using url_launcher
        final phoneNumber = contact.deviceContact.phoneNumbers.isNotEmpty
            ? contact.deviceContact.phoneNumbers.first
            : '';
        if (phoneNumber.isNotEmpty) {
          final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
          if (await canLaunchUrl(phoneUri)) {
            await launchUrl(phoneUri);
          }
        }
        return;
      }

      // Check if the CONTACT (person being called) is in emergency mode
      EmergencyStatus? contactEmergencyStatus;

      try {
        contactEmergencyStatus =
            await EmergencyModeService.checkUserEmergencyStatus(
                contact.profile!.id);
      } catch (e) {
        print('⚠️ Contact emergency status check failed: $e');
        // If emergency check fails, assume contact is NOT in emergency mode
        contactEmergencyStatus = null;
      }

      // 🧪 TEMPORARY TEST: Simulate emergency mode for specific contact
      // TODO: Remove this test code once database schema is fixed
      if (contact.profile?.fullName == 'mam') {
        print(
            '🧪 TEST: Simulating emergency mode for contact: ${contact.profile?.fullName}');
        contactEmergencyStatus = EmergencyStatus(
          isActive: true,
          startTime: DateTime.now().subtract(Duration(minutes: 30)),
          expiryTime: DateTime.now().add(Duration(hours: 2)),
          exceptionGroups: ['family', 'co-workers'],
          userName: contact.profile?.fullName ?? 'Test User',
        );
      }

      if (contactEmergencyStatus != null && contactEmergencyStatus.isActive) {
        if (context.mounted) {
          await _showContactEmergencyDialog(
              context, contact, contactEmergencyStatus);
        }
        return;
      }

      // Get how the CONTACT has categorized the CURRENT USER
      // This represents the contact's preferences for being contacted by the current user
      // Pass all contact's phone numbers for better matching
      final contactCategory =
          await _offlineService.getContactCategoryWithPhones(
        contactUserId: contact.profile!.id,
        callerPhone: currentProfile.phoneNumber,
        contactPhoneNumbers: contact.deviceContact.phoneNumbers,
      );

      // Show calling suggestion dialog
      if (context.mounted) {
        final shouldProceed = await _showCallingSuggestionDialog(
            context, contact, contactCategory);

        if (shouldProceed == true) {
          // Handle multiple phone numbers for contacts with app accounts
          if (contact.deviceContact.phoneNumbers.length == 1) {
            // Only one phone number, proceed with normal call
            await CallingService.makeCall(
              contactProfile: contact.profile!,
              contactCategory: contactCategory,
              callerName: currentProfile.fullName ?? currentProfile.phoneNumber,
            );
          } else {
            // Multiple phone numbers, show selection dialog for app users
            if (context.mounted) {
              await _showPhoneSelectionDialogForAppUser(
                  context, contact, contactCategory, currentProfile);
            }
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                AppLocalizations.of(context)?.failedToMakeCall(e.toString()) ??
                    'Failed to make call: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<bool?> _showCallingSuggestionDialog(
      BuildContext context, ContactWithProfile contact, Category? category) {
    final isGoodTime = SupabaseService.isGoodTimeToContact(category);
    final suggestion = SupabaseService.getContactSuggestion(category, context);

    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return _CallingSuggestionDialog(
          contact: contact,
          category: category,
          isGoodTime: isGoodTime,
          suggestion: suggestion,
        );
      },
    );
  }

  Future<void> _showPhoneSelectionDialogForAppUser(
      BuildContext context,
      ContactWithProfile contact,
      Category? contactCategory,
      Profile currentProfile) async {
    return showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: _getDialogGradientColors(contactCategory),
            ),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.green.shade400, Colors.green.shade600],
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.green.withOpacity(0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.phone,
                  color: Colors.white,
                  size: 30,
                ),
              ),
              const SizedBox(height: 20),

              Text(
                AppLocalizations.of(context)?.selectPhoneNumber ??
                    'Select Phone Number',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),

              // Phone numbers list
              for (final phoneNumber in contact.deviceContact.phoneNumbers)
                Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(16),
                      onTap: () async {
                        Navigator.of(context).pop();
                        // Call CallingService.makeCall with the selected phone number
                        await CallingService.makeCall(
                          contactProfile: contact.profile!,
                          contactCategory: contactCategory,
                          callerName: currentProfile.fullName ??
                              currentProfile.phoneNumber,
                          specificPhoneNumber:
                              phoneNumber, // Use the selected phone number
                        );
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: Colors.green.shade100,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.phone,
                                color: Colors.green.shade600,
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Text(
                                ContactsService.formatPhoneNumber(phoneNumber),
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey[800],
                                ),
                              ),
                            ),
                            Icon(
                              Icons.arrow_forward_ios,
                              color: Colors.grey[400],
                              size: 16,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

              const SizedBox(height: 20),

              // Cancel button
              Container(
                width: double.infinity,
                height: 50,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(12),
                    onTap: () => Navigator.of(context).pop(),
                    child: Center(
                      child: Text(
                        AppLocalizations.of(context)?.cancel ?? 'Cancel',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Show emergency mode dialog when user tries to call during emergency
  Future<void> _showEmergencyModeDialog(BuildContext context,
      ContactWithProfile contact, EmergencyModeService emergencyService) async {
    await showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(28),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.9,
          ),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFFFF6B6B), // Red
                Color(0xFFFF8E8E), // Light red
                Color(0xFFFFB3B3), // Very light red
              ],
            ),
            borderRadius: BorderRadius.circular(28),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFFF6B6B).withOpacity(0.4),
                blurRadius: 30,
                offset: const Offset(0, 15),
                spreadRadius: 5,
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(28),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Emergency icon
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.emergency,
                    size: 40,
                    color: Colors.white,
                  ),
                ),

                const SizedBox(height: 24),

                // Title
                Text(
                  AppLocalizations.of(context)?.emergencyModeActive ??
                      'Emergency Mode Active',
                  style: const TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 16),

                // Contact name
                Text(
                  AppLocalizations.of(context)
                          ?.cannotContact(contact.displayName) ??
                      'Cannot contact ${contact.displayName}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 12),

                // Emergency message
                Text(
                  emergencyService
                      .getEmergencyMessage(AppLocalizations.of(context)),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.9),
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 24),

                // Close button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: const Color(0xFFFF6B6B),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      AppLocalizations.of(context)?.understood ?? 'Understood',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Show emergency dialog when trying to contact someone who is in emergency mode
  Future<void> _showContactEmergencyDialog(BuildContext context,
      ContactWithProfile contact, EmergencyStatus emergencyStatus) async {
    await showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(32),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 40),
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFFFF6B6B), // Red
                Color(0xFFFF8E8E), // Light red
                Color(0xFFFFB3B3), // Very light red
              ],
              stops: [0.0, 0.6, 1.0],
            ),
            borderRadius: BorderRadius.circular(32),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFFF6B6B).withOpacity(0.4),
                blurRadius: 30,
                offset: const Offset(0, 15),
                spreadRadius: 5,
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(32),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(28),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Emergency icon with animation
                  TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 1500),
                    tween: Tween(begin: 0.8, end: 1.0),
                    curve: Curves.easeInOut,
                    builder: (context, scale, child) {
                      return Transform.scale(
                        scale: scale,
                        child: Container(
                          width: 90,
                          height: 90,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.25),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white.withOpacity(0.3),
                              width: 2,
                            ),
                          ),
                          child: const Icon(
                            Icons.emergency,
                            size: 45,
                            color: Colors.white,
                          ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 24),

                  // Title
                  Text(
                    AppLocalizations.of(context)?.emergencyModeActive ??
                        'Emergency Mode Active',
                    style: const TextStyle(
                      fontSize: 26,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      letterSpacing: -0.5,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 16),

                  // Contact name
                  Text(
                    AppLocalizations.of(context)
                            ?.cannotContact(contact.displayName) ??
                        'Cannot contact ${contact.displayName}',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 20),

                  // Emergency message
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        // Main emergency message
                        Text(
                          '${emergencyStatus.userName} في وضع الطوارئ حالياً. غير متاح للمكالمات أو الرسائل.',
                          style: TextStyle(
                            fontSize: 15,
                            color: Colors.white.withOpacity(0.95),
                            height: 1.5,
                            fontWeight: FontWeight.w400,
                          ),
                          textAlign: TextAlign.center,
                        ),

                        // Exception groups (highlighted)
                        if (emergencyStatus.exceptionGroups.isNotEmpty) ...[
                          const SizedBox(height: 16),
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.orange.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.orange.withOpacity(0.4),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.group,
                                  color: Colors.orange.shade300,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'المجموعات المستثناة: ${emergencyStatus.exceptionGroups.map((group) => EmergencyModeService.getLocalizedGroupName(group, AppLocalizations.of(context))).join('، ')}',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.orange.shade200,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],

                        // Remaining time
                        const SizedBox(height: 16),
                        Text(
                          'وضع الطوارئ: ${EmergencyModeService.getFormattedRemainingTimeForUser(emergencyStatus, AppLocalizations.of(context))}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white.withOpacity(0.8),
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Close button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white.withOpacity(0.95),
                        foregroundColor: const Color(0xFFFF6B6B),
                        padding: const EdgeInsets.symmetric(vertical: 18),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        elevation: 0,
                        shadowColor: Colors.transparent,
                      ),
                      child: Text(
                        AppLocalizations.of(context)?.understood ??
                            'Understood',
                        style: const TextStyle(
                          fontSize: 17,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _CallingSuggestionDialog extends StatefulWidget {
  final ContactWithProfile contact;
  final Category? category;
  final bool isGoodTime;
  final String suggestion;

  const _CallingSuggestionDialog({
    required this.contact,
    required this.category,
    required this.isGoodTime,
    required this.suggestion,
  });

  @override
  State<_CallingSuggestionDialog> createState() =>
      _CallingSuggestionDialogState();
}

class _CallingSuggestionDialogState extends State<_CallingSuggestionDialog> {
  Set<String> _enabledNotifications = {};
  bool _isLoadingNotifications = true;

  @override
  void initState() {
    super.initState();
    _loadNotificationPreferences();
  }

  Future<void> _loadNotificationPreferences() async {
    try {
      final currentProfile = await OfflineContactService().getCurrentProfile();

      if (currentProfile != null &&
          widget.category != null &&
          widget.contact.profile != null) {
        try {
          final preferences =
              await OfflineContactService().getNotificationPreferences(
            userId: currentProfile.id,
            contactUserId: widget.contact.profile!.id,
          );

          if (mounted) {
            setState(() {
              _enabledNotifications = preferences
                  .where((pref) => pref.isEnabled)
                  .map((pref) => pref.timeSlotId)
                  .toSet()
                  .cast<String>();
              _isLoadingNotifications = false;
            });
          }
        } catch (e) {
          print('❌ Failed to load notification preferences: $e');
          if (mounted) {
            setState(() {
              _enabledNotifications = {};
              _isLoadingNotifications = false;
            });
          }
        }
      } else {
        // No profile or category - skip notification preferences
        if (mounted) {
          setState(() {
            _enabledNotifications = {};
            _isLoadingNotifications = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingNotifications = false;
        });
      }
    }
  }

  Future<void> _toggleNotification(String timeSlotId) async {
    try {
      final currentProfile = await OfflineContactService().getCurrentProfile();
      if (currentProfile == null || widget.contact.profile == null) return;

      final isCurrentlyEnabled = _enabledNotifications.contains(timeSlotId);
      final newState = !isCurrentlyEnabled;
      final connectivityService = ConnectivityService();

      // Use offline-aware service
      try {
        await OfflineContactService().updateNotificationPreference(
          userId: currentProfile.id,
          contactUserId: widget.contact.profile!.id,
          timeSlotId: timeSlotId,
          isEnabled: newState,
        );
        print('✅ Notification preference updated');
      } catch (e) {
        print('❌ Failed to update notification preference: $e');
      }

      if (mounted) {
        setState(() {
          if (newState) {
            _enabledNotifications.add(timeSlotId);
          } else {
            _enabledNotifications.remove(timeSlotId);
          }
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              newState
                  ? (connectivityService.isOnline
                      ? (AppLocalizations.of(context)?.notificationEnabled ??
                          'Notification enabled for this time slot')
                      : (AppLocalizations.of(context)
                              ?.notificationEnabledOffline ??
                          'Notification enabled for this time slot (offline)'))
                  : (connectivityService.isOnline
                      ? (AppLocalizations.of(context)?.notificationDisabled ??
                          'Notification disabled for this time slot')
                      : (AppLocalizations.of(context)
                              ?.notificationDisabledOffline ??
                          'Notification disabled for this time slot (offline)')),
            ),
            backgroundColor: newState ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 2),
          ),
        );

        // Reschedule notifications (works both online and offline)
        print(
            '📱 Scheduling notifications in ${connectivityService.isOnline ? 'online' : 'offline'} mode...');
        try {
          await NotificationService.scheduleContactNotifications();
          print('✅ Notifications scheduled successfully');
        } catch (e) {
          print('❌ Failed to schedule notifications: $e');
          // Don't show error to user as the preference was still saved
        }
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'Error updating notification';
        if (e.toString().contains('duplicate key')) {
          errorMessage =
              'Notification preference already exists. Please try again.';
        } else if (e.toString().contains('network') ||
            e.toString().contains('connection')) {
          errorMessage =
              'Network error. Please check your connection and try again.';
        } else {
          errorMessage = 'Error updating notification: ${e.toString()}';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  // Get shadow color based on category
  Color _getCategoryShadowColor() {
    if (widget.category == null) {
      return widget.isGoodTime
          ? Colors.green.withOpacity(0.3)
          : Colors.orange.withOpacity(0.3);
    }

    final categoryColor = widget.category!.type.getColor();
    return categoryColor.withOpacity(0.3);
  }

  // Get icon gradient colors based on category
  List<Color> _getIconGradientColors() {
    if (widget.category == null) {
      final baseColor = widget.isGoodTime ? Colors.green : Colors.orange;
      return [baseColor, baseColor.withOpacity(0.8)];
    }

    final categoryColor = widget.category!.type.getColor();
    return [categoryColor, categoryColor.withOpacity(0.8)];
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: ModernContactCard.getDialogGradientColors(widget.category),
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: _getCategoryShadowColor(),
              blurRadius: 25,
              offset: const Offset(0, 12),
              spreadRadius: 2,
            ),
            BoxShadow(
              color: Colors.white.withOpacity(0.8),
              blurRadius: 10,
              offset: const Offset(-5, -5),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with icon and title
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: _getIconGradientColors(),
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: _getCategoryShadowColor(),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Icon(
                      widget.isGoodTime
                          ? Icons.phone_enabled
                          : Icons.phone_disabled,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context)?.callContactQuestion(
                                  widget.contact.displayName) ??
                              'Call ${widget.contact.displayName}?',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                            color: Colors.grey[800],
                          ),
                        ),
                        Text(
                          widget.isGoodTime
                              ? (AppLocalizations.of(context)?.goodTimeToCall ??
                                  'Good time to call')
                              : (AppLocalizations.of(context)?.notIdealTiming ??
                                  'Not ideal timing'),
                          style: TextStyle(
                            fontSize: 14,
                            color: widget.isGoodTime
                                ? Colors.green[600]
                                : Colors.orange[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              // Suggestion card
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      widget.isGoodTime
                          ? Colors.green.shade50
                          : Colors.orange.shade50,
                      widget.isGoodTime
                          ? Colors.green.shade100
                          : Colors.orange.shade100,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: widget.isGoodTime
                        ? Colors.green.shade200
                        : Colors.orange.shade200,
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: widget.isGoodTime
                          ? Colors.green.withOpacity(0.1)
                          : Colors.orange.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  widget.suggestion,
                  style: TextStyle(
                    fontSize: 16,
                    color: widget.isGoodTime
                        ? Colors.green[800]
                        : Colors.orange[800],
                    height: 1.5,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (widget.category?.timeSlots.isNotEmpty == true) ...[
                const SizedBox(height: 20),
                Flexible(
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.grey.shade200,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.schedule,
                              color: Colors.grey[600],
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              AppLocalizations.of(context)
                                      ?.theirPreferredTimes ??
                                  'Their preferred times:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        if (_isLoadingNotifications)
                          const Center(child: CircularProgressIndicator())
                        else if (widget.category != null)
                          Flexible(
                            child: SingleChildScrollView(
                              child: Column(
                                children: widget.category!.timeSlots
                                    .map((slot) => _buildTimeSlotRow(slot))
                                    .toList(),
                              ),
                            ),
                          )
                        else
                          Text(
                            AppLocalizations.of(context)
                                    ?.noTimeSlotsAvailable ??
                                'No time slots available',
                            style: TextStyle(
                              color: Colors.grey,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ],
              const SizedBox(height: 24),
              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      child: Text(
                        AppLocalizations.of(context)?.cancel ?? 'Cancel',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: widget.isGoodTime
                            ? Colors.green[600]
                            : Colors.orange[600],
                        foregroundColor: Colors.white,
                        elevation: 8,
                        shadowColor: widget.isGoodTime
                            ? Colors.green.withOpacity(0.3)
                            : Colors.orange.withOpacity(0.3),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.phone, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            widget.isGoodTime
                                ? (AppLocalizations.of(context)?.callNow ??
                                    'Call Now')
                                : (AppLocalizations.of(context)?.callAnyway ??
                                    'Call Anyway'),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTimeSlotRow(TimeSlot slot) {
    final isActive = slot.isCurrentTimeInSlot();
    final isNotificationEnabled = _enabledNotifications.contains(slot.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            isActive ? Colors.green.shade50 : Colors.white,
            isActive ? Colors.green.shade100 : Colors.grey.shade100,
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isActive ? Colors.green.shade300 : Colors.grey.shade200,
          width: isActive ? 2 : 1,
        ),
        boxShadow: [
          if (isActive) ...[
            BoxShadow(
              color: Colors.green.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ] else ...[
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  isActive ? Colors.green.shade400 : Colors.grey.shade400,
                  isActive ? Colors.green.shade600 : Colors.grey.shade500,
                ],
              ),
              shape: BoxShape.circle,
              boxShadow: [
                if (isActive)
                  BoxShadow(
                    color: Colors.green.withOpacity(0.4),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${slot.getDayName(context)}: ${slot.timeRange}',
                  style: TextStyle(
                    fontSize: 14,
                    color: isActive ? Colors.green[700] : Colors.grey[700],
                    fontWeight: isActive ? FontWeight.w700 : FontWeight.w500,
                  ),
                ),
                if (isActive) ...[
                  const SizedBox(height: 4),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.green.shade400, Colors.green.shade600],
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      AppLocalizations.of(context)?.activeNowLabel ??
                          'ACTIVE NOW',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  isNotificationEnabled
                      ? Colors.orange.shade100
                      : Colors.grey.shade100,
                  isNotificationEnabled
                      ? Colors.orange.shade50
                      : Colors.grey.shade50,
                ],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isNotificationEnabled
                    ? Colors.orange.shade300
                    : Colors.grey.shade300,
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: isNotificationEnabled
                      ? Colors.orange.withOpacity(0.2)
                      : Colors.grey.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(12),
                onTap: () => _toggleNotification(slot.id),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isNotificationEnabled
                            ? Icons.notifications_active
                            : Icons.notifications_none,
                        color: isNotificationEnabled
                            ? Colors.orange[700]
                            : Colors.grey[600],
                        size: 18,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        isNotificationEnabled
                            ? (AppLocalizations.of(context)?.on ?? 'ON')
                            : (AppLocalizations.of(context)?.off ?? 'OFF'),
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.bold,
                          color: isNotificationEnabled
                              ? Colors.orange[700]
                              : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _CategorySelectionDialog extends StatelessWidget {
  final List<Category> categories;
  final Category? currentCategory;
  final String contactName;

  const _CategorySelectionDialog({
    required this.categories,
    required this.currentCategory,
    required this.contactName,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.white.withOpacity(0.95),
            ],
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.blue.withOpacity(0.3),
              blurRadius: 25,
              offset: const Offset(0, 12),
              spreadRadius: 2,
            ),
            BoxShadow(
              color: Colors.white.withOpacity(0.8),
              blurRadius: 10,
              offset: const Offset(-5, -5),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with icon and title
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.blue,
                          Colors.blue.withOpacity(0.8),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.blue.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.category,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context)?.assignCategory ??
                              'Assign Category',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                            color: Colors.grey[800],
                          ),
                        ),
                        Text(
                          AppLocalizations.of(context)
                                  ?.chooseHowToCategorize(contactName) ??
                              'Choose how to categorize $contactName',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.blue[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              // Categories list
              Flexible(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.grey.shade200,
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.list_alt,
                            color: Colors.grey[600],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            AppLocalizations.of(context)?.availableCategories ??
                                'Available Categories:',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Flexible(
                        child: SingleChildScrollView(
                          child: Column(
                            children: categories
                                .map((category) =>
                                    _buildCategoryOption(context, category))
                                .toList(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      child: Text(
                        AppLocalizations.of(context)?.cancel ?? 'Cancel',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryOption(BuildContext context, Category category) {
    final isSelected = currentCategory?.id == category.id;
    final categoryColor = _getCategoryColor(category.type);
    final categoryIcon = _getCategoryIcon(category.type);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => Navigator.of(context).pop(category),
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: isSelected
                  ? LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        categoryColor.withOpacity(0.1),
                        categoryColor.withOpacity(0.05),
                      ],
                    )
                  : LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white,
                        Colors.grey[50]!,
                      ],
                    ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected
                    ? categoryColor.withOpacity(0.5)
                    : Colors.grey.shade200,
                width: isSelected ? 2 : 1,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: categoryColor.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ]
                  : [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        categoryColor,
                        categoryColor.withOpacity(0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: categoryColor.withOpacity(0.3),
                        blurRadius: 6,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Icon(
                    categoryIcon,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category.type.getDisplayName(context),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: isSelected ? categoryColor : Colors.grey[800],
                        ),
                      ),
                      const SizedBox(height: 6),
                      Text(
                        category.type.getDefaultNote(context),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: categoryColor,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: categoryColor.withOpacity(0.3),
                          blurRadius: 6,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getCategoryIcon(CategoryType type) {
    switch (type) {
      case CategoryType.contactAnytime:
        return Icons.access_time;
      case CategoryType.preferAnytime:
        return Icons.star;
      case CategoryType.contactAtTimes:
        return Icons.schedule;
      case CategoryType.contactThroughMessages:
        return Icons.message;
    }
  }

  Color _getCategoryColor(CategoryType type) {
    switch (type) {
      case CategoryType.contactAnytime:
        return Colors.green;
      case CategoryType.preferAnytime:
        return Colors.blue;
      case CategoryType.contactAtTimes:
        return Colors.orange;
      case CategoryType.contactThroughMessages:
        return Colors.purple;
    }
  }
}

class _NonAppUserDialog extends StatelessWidget {
  final ContactWithProfile contact;

  const _NonAppUserDialog({required this.contact});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.white.withOpacity(0.95),
            ],
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.blue.withOpacity(0.3),
              blurRadius: 25,
              offset: const Offset(0, 12),
              spreadRadius: 2,
            ),
            BoxShadow(
              color: Colors.white.withOpacity(0.8),
              blurRadius: 10,
              offset: const Offset(-5, -5),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with icon and title
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.blue,
                          Colors.blue.withOpacity(0.8),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.blue.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.person_off,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          contact.displayName,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        Text(
                          AppLocalizations.of(context)?.notUsingTheApp ??
                              'Not using the app',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Description
              Text(
                AppLocalizations.of(context)
                        ?.contactNotUsingAppYet(contact.displayName) ??
                    '${contact.displayName} is not using this app yet.',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                AppLocalizations.of(context)?.whatWouldYouLikeToDo ??
                    'What would you like to do?',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // Action buttons
              Row(
                children: [
                  // Cancel button
                  Expanded(
                    child: Container(
                      height: 48,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(12),
                          onTap: () => Navigator.of(context).pop(),
                          child: Center(
                            child: Text(
                              AppLocalizations.of(context)?.cancel ?? 'Cancel',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Share button
                  Expanded(
                    child: Container(
                      height: 48,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue.shade400, Colors.blue.shade600],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(12),
                          onTap: () {
                            Navigator.of(context).pop();
                            _shareApp(context, contact);
                          },
                          child: Center(
                            child: Text(
                              AppLocalizations.of(context)?.share ?? 'Share',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Call button
                  Expanded(
                    child: Container(
                      height: 48,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.green.shade400,
                            Colors.green.shade600
                          ],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.green.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(12),
                          onTap: () {
                            Navigator.of(context).pop();
                            _callNow(context, contact);
                          },
                          child: Center(
                            child: Text(
                              AppLocalizations.of(context)?.callNow ??
                                  'Call Now',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _shareApp(BuildContext context, ContactWithProfile contact) async {
    // Show custom dialog to choose sharing method
    await _showShareOptionsDialog(context, contact);
  }

  Future<void> _showShareOptionsDialog(
      BuildContext context, ContactWithProfile contact) async {
    const appLink =
        'https://play.google.com/store/apps/details?id=com.example.contact_times';
    final l10n = AppLocalizations.of(context)!;
    final message = l10n.shareMessage(appLink);

    await showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(24),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue, Colors.blue.withOpacity(0.8)],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Icon(
                Icons.share,
                color: Colors.white,
                size: 28,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                AppLocalizations.of(context)?.shareApp ?? 'Share App',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              l10n.chooseHowToShare(contact.displayName),
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),

            // WhatsApp Option
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 12),
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  _shareViaWhatsApp(context, contact, message);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF25D366), // WhatsApp green
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                icon: const Icon(Icons.chat, size: 24),
                label: Text(
                  l10n.whatsapp,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),

            // SMS Option
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 12),
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  _shareViaSMS(context, contact, message);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                icon: const Icon(Icons.sms, size: 24),
                label: Text(
                  l10n.sms,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),

            // Other Apps Option
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 12),
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  _shareViaOtherApps(context, message);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                icon: const Icon(Icons.more_horiz, size: 24),
                label: Text(
                  l10n.otherApps,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),

            // Copy Link Option
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  _copyAppLink(context, appLink);
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  side: BorderSide(color: Colors.grey[400]!),
                ),
                icon: Icon(Icons.copy, color: Colors.grey[600]),
                label: Text(
                  l10n.copyLink,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[600],
                  ),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancel),
          ),
        ],
      ),
    );
  }

  Future<void> _shareViaWhatsApp(
      BuildContext context, ContactWithProfile contact, String message) async {
    try {
      // Copy message to clipboard first
      await Clipboard.setData(ClipboardData(text: message));

      // Open WhatsApp contacts list instead of specific contact
      // This allows user to choose who to share with
      const whatsappUrl = 'whatsapp://';
      final uri = Uri.parse(whatsappUrl);

      print(
          '📱 Attempting to open WhatsApp contacts list with URL: $whatsappUrl');

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)
                      ?.whatsappOpenedWithMessage ??
                  'WhatsApp opened. Message copied to clipboard - paste it to share!'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      } else {
        throw Exception('WhatsApp is not installed or cannot be opened');
      }
    } catch (e) {
      print('❌ Error opening WhatsApp: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to open WhatsApp: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
            action: SnackBarAction(
              label: 'Try SMS',
              textColor: Colors.white,
              onPressed: () => _shareViaSMS(context, contact, message),
            ),
          ),
        );
      }
    }
  }

  Future<void> _shareViaSMS(
      BuildContext context, ContactWithProfile contact, String message) async {
    try {
      await CallingService.sendSMS(contact.primaryPhoneNumber, message);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('SMS app opened successfully'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      print('❌ Error opening SMS: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to open SMS: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _shareViaOtherApps(BuildContext context, String message) async {
    try {
      await Share.share(
        message,
        subject: 'Contact Times App - Manage Your Contact Preferences',
      );
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Share dialog opened'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      print('❌ Error sharing via other apps: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to open share dialog: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _copyAppLink(BuildContext context, String appLink) async {
    try {
      await Clipboard.setData(ClipboardData(text: appLink));
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('App link copied to clipboard'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      print('❌ Error copying to clipboard: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to copy link: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _callNow(BuildContext context, ContactWithProfile contact) async {
    try {
      if (contact.deviceContact.phoneNumbers.length == 1) {
        // Only one phone number, call directly
        await CallingService.launchDialer(
            contact.deviceContact.phoneNumbers.first);
      } else {
        // Multiple phone numbers, show selection dialog
        await _showPhoneSelectionDialog(context, contact);
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                AppLocalizations.of(context)?.failedToMakeCall(e.toString()) ??
                    'Failed to make call: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _showPhoneSelectionDialog(
      BuildContext context, ContactWithProfile contact) async {
    return showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: ModernContactCard.getDialogGradientColors(
                  null), // No category for non-app users
            ),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.green.shade400, Colors.green.shade600],
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.green.withOpacity(0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.phone,
                  color: Colors.white,
                  size: 30,
                ),
              ),
              const SizedBox(height: 20),

              Text(
                AppLocalizations.of(context)?.selectPhoneNumber ??
                    'Select Phone Number',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),

              // Phone numbers list
              for (final phoneNumber in contact.deviceContact.phoneNumbers)
                Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(16),
                      onTap: () async {
                        Navigator.of(context).pop();
                        await CallingService.launchDialer(phoneNumber);
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: Colors.green.shade100,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.phone,
                                color: Colors.green.shade600,
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Text(
                                ContactsService.formatPhoneNumber(phoneNumber),
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey[800],
                                ),
                              ),
                            ),
                            Icon(
                              Icons.arrow_forward_ios,
                              color: Colors.grey[400],
                              size: 16,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

              const SizedBox(height: 20),

              // Cancel button
              Container(
                width: double.infinity,
                height: 48,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(12),
                    onTap: () => Navigator.of(context).pop(),
                    child: Center(
                      child: Text(
                        AppLocalizations.of(context)?.cancel ?? 'Cancel',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Emergency Activation Dialog
class _EmergencyActivationDialog extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final maxDialogHeight = screenHeight * 0.85; // Use 85% of screen height

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(32),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 40),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxHeight: maxDialogHeight,
        ),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFFF6B6B), // Red
              Color(0xFFFF8E8E), // Light red
              Color(0xFFFFB3B3), // Very light red
            ],
            stops: [0.0, 0.6, 1.0],
          ),
          borderRadius: BorderRadius.circular(32),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFFF6B6B).withOpacity(0.4),
              blurRadius: 30,
              offset: const Offset(0, 15),
              spreadRadius: 5,
            ),
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(32),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Fixed header section
              Container(
                padding: const EdgeInsets.fromLTRB(28, 28, 28, 20),
                child: Column(
                  children: [
                    // Emergency icon with pulse animation
                    TweenAnimationBuilder<double>(
                      duration: const Duration(milliseconds: 1500),
                      tween: Tween(begin: 0.8, end: 1.0),
                      curve: Curves.easeInOut,
                      builder: (context, scale, child) {
                        return Transform.scale(
                          scale: scale,
                          child: Container(
                            width: 90,
                            height: 90,
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.25),
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Colors.white.withOpacity(0.3),
                                width: 2,
                              ),
                            ),
                            child: const Icon(
                              Icons.emergency,
                              size: 45,
                              color: Colors.white,
                            ),
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 24),

                    // Title
                    Text(
                      AppLocalizations.of(context)?.activateEmergencyMode ??
                          'Activate Emergency Mode',
                      style: const TextStyle(
                        fontSize: 26,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        letterSpacing: -0.5,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 12),

                    // Description
                    Text(
                      AppLocalizations.of(context)?.emergencyModeDescription ??
                          'During emergency mode, contacts will see a special message when trying to call or message you.',
                      style: TextStyle(
                        fontSize: 15,
                        color: Colors.white.withOpacity(0.9),
                        height: 1.5,
                        fontWeight: FontWeight.w400,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 24),

                    // Duration selection label
                    Row(
                      children: [
                        Icon(
                          Icons.schedule,
                          color: Colors.white.withOpacity(0.9),
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          AppLocalizations.of(context)?.selectDuration ??
                              'Select Duration:',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Scrollable duration options
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.fromLTRB(28, 0, 28, 20),
                  child: Column(
                    children: [
                      // Duration options with modern cards
                      ...EmergencyDuration.values.map(
                        (duration) => Container(
                          width: double.infinity,
                          margin: const EdgeInsets.only(bottom: 12),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(20),
                              onTap: () => Navigator.of(context).pop(duration),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 18,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.95),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Colors.white.withOpacity(0.3),
                                    width: 1,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.1),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Row(
                                  children: [
                                    // Duration icon
                                    Container(
                                      width: 44,
                                      height: 44,
                                      decoration: BoxDecoration(
                                        color: const Color(0xFFFF6B6B)
                                            .withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Center(
                                        child: Text(
                                          duration.icon,
                                          style: const TextStyle(fontSize: 22),
                                        ),
                                      ),
                                    ),

                                    const SizedBox(width: 16),

                                    // Duration text
                                    Expanded(
                                      child: Text(
                                        duration.getLocalizedDisplayName(
                                            AppLocalizations.of(context)),
                                        style: const TextStyle(
                                          fontSize: 17,
                                          fontWeight: FontWeight.w600,
                                          color: Color(0xFFFF6B6B),
                                        ),
                                      ),
                                    ),

                                    // Arrow icon
                                    Icon(
                                      Icons.arrow_forward_ios,
                                      color: const Color(0xFFFF6B6B)
                                          .withOpacity(0.6),
                                      size: 16,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Cancel button
                      SizedBox(
                        width: double.infinity,
                        child: TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 18),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                              side: BorderSide(
                                color: Colors.white.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                          ),
                          child: Text(
                            AppLocalizations.of(context)?.cancel ?? 'Cancel',
                            style: const TextStyle(
                              fontSize: 17,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Emergency Status Dialog (when already active)
class _EmergencyStatusDialog extends StatelessWidget {
  final EmergencyModeService emergencyService;

  const _EmergencyStatusDialog({required this.emergencyService});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(28),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFFF6B6B), // Red
              Color(0xFFFF8E8E), // Light red
            ],
          ),
          borderRadius: BorderRadius.circular(28),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFFF6B6B).withOpacity(0.4),
              blurRadius: 30,
              offset: const Offset(0, 15),
              spreadRadius: 5,
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(28),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Emergency icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.emergency,
                  size: 40,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: 24),

              // Title
              Text(
                AppLocalizations.of(context)?.emergencyModeActive ??
                    'Emergency Mode Active',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 12),

              // Remaining time
              Text(
                emergencyService.getLocalizedFormattedRemainingTime(context),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 16),

              // Description
              Text(
                AppLocalizations.of(context)?.emergencyContactsMessage ??
                    'Contacts will see an emergency message when trying to reach you.',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withOpacity(0.9),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 28),

              // Deactivate button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () async {
                    await emergencyService.deactivateEmergencyMode();
                    if (context.mounted) {
                      Navigator.of(context).pop();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: const Color(0xFFFF6B6B),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    AppLocalizations.of(context)?.deactivateEmergencyMode ??
                        'Deactivate Emergency Mode',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 12),

              // Close button
              SizedBox(
                width: double.infinity,
                child: TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Text(
                    AppLocalizations.of(context)?.close ?? 'Close',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Group Selection Dialog for Emergency Mode
class _GroupSelectionDialog extends StatefulWidget {
  final EmergencyModeService emergencyService;

  const _GroupSelectionDialog({
    required this.emergencyService,
  });

  @override
  State<_GroupSelectionDialog> createState() => _GroupSelectionDialogState();
}

class _GroupSelectionDialogState extends State<_GroupSelectionDialog> {
  final Set<String> _selectedGroups = {};
  final TextEditingController _customGroupController = TextEditingController();
  List<String> _availableGroups = [];

  @override
  void initState() {
    super.initState();
    _loadAvailableGroups();
  }

  void _loadAvailableGroups() {
    _availableGroups = widget.emergencyService.getAllAvailableGroups();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF6366F1), // Indigo
              Color(0xFF8B5CF6), // Purple
              Color(0xFF3B82F6), // Blue
            ],
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Title
              Text(
                AppLocalizations.of(context)?.selectExceptionGroups ??
                    'Select Exception Groups',
                style: const TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 16),

              // Description
              Text(
                AppLocalizations.of(context)?.exceptionGroupsDescription ??
                    'Select groups that can still contact you during emergency mode',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withOpacity(0.9),
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 24),

              // Groups list
              Container(
                constraints: const BoxConstraints(maxHeight: 300),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      ..._availableGroups
                          .map((group) => _buildGroupTile(group)),

                      // Add custom group section
                      const SizedBox(height: 16),
                      _buildAddCustomGroupSection(),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Buttons
              Row(
                children: [
                  // Cancel button
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(12),
                          onTap: () => Navigator.of(context).pop(),
                          child: Center(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              child: Text(
                                AppLocalizations.of(context)?.cancel ??
                                    'Cancel',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Confirm button
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(12),
                          onTap: () => Navigator.of(context)
                              .pop(_selectedGroups.toList()),
                          child: Center(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              child: Text(
                                AppLocalizations.of(context)?.confirm ??
                                    'Confirm',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF6366F1),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGroupTile(String group) {
    final isSelected = _selectedGroups.contains(group);
    final isCustom = !EmergencyModeService.defaultGroups.contains(group);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isSelected
            ? Colors.white.withOpacity(0.3)
            : Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
              ? Colors.white.withOpacity(0.6)
              : Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            setState(() {
              if (isSelected) {
                _selectedGroups.remove(group);
              } else {
                _selectedGroups.add(group);
              }
            });
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Checkbox
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.white : Colors.transparent,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: Colors.white,
                      width: 2,
                    ),
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          size: 14,
                          color: Color(0xFF6366F1),
                        )
                      : null,
                ),

                const SizedBox(width: 12),

                // Group name
                Expanded(
                  child: Text(
                    _getLocalizedGroupName(group),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),

                // Delete button for custom groups
                if (isCustom)
                  IconButton(
                    icon: const Icon(
                      Icons.delete_outline,
                      color: Colors.white,
                      size: 20,
                    ),
                    onPressed: () async {
                      await widget.emergencyService.removeCustomGroup(group);
                      setState(() {
                        _loadAvailableGroups();
                        _selectedGroups.remove(group);
                      });
                    },
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAddCustomGroupSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)?.addCustomGroup ??
                'Create New Group or Note',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context)?.addCustomGroupDescription ??
                'You can create custom groups (e.g., \'Work Team\') or personal notes (e.g., \'Important Meeting\')',
            style: TextStyle(
              fontSize: 12,
              color: Colors.white.withOpacity(0.8),
              height: 1.3,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _customGroupController,
                  style: const TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    hintText:
                        AppLocalizations.of(context)?.groupName ?? 'Group name',
                    hintStyle: TextStyle(color: Colors.white.withOpacity(0.6)),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide:
                          BorderSide(color: Colors.white.withOpacity(0.3)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide:
                          BorderSide(color: Colors.white.withOpacity(0.3)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Colors.white),
                    ),
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: () async {
                  final groupName = _customGroupController.text.trim();
                  if (groupName.isNotEmpty) {
                    await widget.emergencyService.addCustomGroup(groupName);
                    setState(() {
                      _loadAvailableGroups();
                      _customGroupController.clear();
                    });
                  }
                },
                icon: const Icon(
                  Icons.add,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getLocalizedGroupName(String group) {
    switch (group) {
      case 'family':
        return AppLocalizations.of(context)?.family ?? 'Family';
      case 'coworkers':
        return AppLocalizations.of(context)?.coworkers ?? 'Coworkers';
      case 'friends':
        return AppLocalizations.of(context)?.friends ?? 'Friends';
      default:
        return group; // Custom group names are returned as-is
    }
  }

  @override
  void dispose() {
    _customGroupController.dispose();
    super.dispose();
  }
}
