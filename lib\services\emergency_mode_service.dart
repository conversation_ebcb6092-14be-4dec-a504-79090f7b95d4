import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'supabase_service.dart';
import 'connectivity_service.dart';

enum EmergencyDuration {
  oneHour,
  threeHours,
  sixHours,
  twelveHours,
  oneDay,
  threeDays,
  oneWeek,
}

extension EmergencyDurationExtension on EmergencyDuration {
  String get displayName {
    switch (this) {
      case EmergencyDuration.oneHour:
        return '1 Hour';
      case EmergencyDuration.threeHours:
        return '3 Hours';
      case EmergencyDuration.sixHours:
        return '6 Hours';
      case EmergencyDuration.twelveHours:
        return '12 Hours';
      case EmergencyDuration.oneDay:
        return '1 Day';
      case EmergencyDuration.threeDays:
        return '3 Days';
      case EmergencyDuration.oneWeek:
        return '1 Week';
    }
  }

  String getLocalizedDisplayName(dynamic localizations) {
    if (localizations == null) return displayName;

    switch (this) {
      case EmergencyDuration.oneHour:
        return localizations.hour1 ?? 'Hour 1';
      case EmergencyDuration.threeHours:
        return localizations.hours3 ?? 'Hours 3';
      case EmergencyDuration.sixHours:
        return localizations.hours6 ?? 'Hours 6';
      case EmergencyDuration.twelveHours:
        return localizations.hours12 ?? 'Hours 12';
      case EmergencyDuration.oneDay:
        return localizations.day1 ?? 'Day 1';
      case EmergencyDuration.threeDays:
        return 'Days 3'; // Not in localization yet
      case EmergencyDuration.oneWeek:
        return localizations.week1 ?? 'Week 1';
    }
  }

  Duration get duration {
    switch (this) {
      case EmergencyDuration.oneHour:
        return const Duration(hours: 1);
      case EmergencyDuration.threeHours:
        return const Duration(hours: 3);
      case EmergencyDuration.sixHours:
        return const Duration(hours: 6);
      case EmergencyDuration.twelveHours:
        return const Duration(hours: 12);
      case EmergencyDuration.oneDay:
        return const Duration(days: 1);
      case EmergencyDuration.threeDays:
        return const Duration(days: 3);
      case EmergencyDuration.oneWeek:
        return const Duration(days: 7);
    }
  }

  String get icon {
    switch (this) {
      case EmergencyDuration.oneHour:
      case EmergencyDuration.threeHours:
      case EmergencyDuration.sixHours:
      case EmergencyDuration.twelveHours:
        return '⏰';
      case EmergencyDuration.oneDay:
      case EmergencyDuration.threeDays:
      case EmergencyDuration.oneWeek:
        return '📅';
    }
  }
}

// Cache entry for emergency status
class EmergencyStatusCache {
  final EmergencyStatus? status;
  final DateTime cachedAt;

  EmergencyStatusCache({
    required this.status,
    required this.cachedAt,
  });

  bool get isExpired {
    // Cache expires after 30 seconds
    return DateTime.now().difference(cachedAt).inSeconds > 30;
  }
}

class EmergencyModeService {
  static final EmergencyModeService _instance =
      EmergencyModeService._internal();
  factory EmergencyModeService() => _instance;
  static EmergencyModeService get instance => _instance;
  EmergencyModeService._internal() {
    // Start cache cleanup timer
    _startCacheCleanup();
  }

  static const String _emergencyModeKey = 'emergency_mode_active';
  static const String _emergencyStartTimeKey = 'emergency_start_time';
  static const String _emergencyDurationKey = 'emergency_duration';
  static const String _exceptionGroupsKey = 'exception_groups';
  static const String _customGroupsKey = 'custom_groups';

  // Predefined groups
  static const List<String> defaultGroups = [
    'family',
    'coworkers',
    'friends',
  ];

  final StreamController<bool> _emergencyModeController =
      StreamController<bool>.broadcast();
  Stream<bool> get emergencyModeStream => _emergencyModeController.stream;

  bool _isEmergencyMode = false;
  DateTime? _emergencyStartTime;
  EmergencyDuration? _emergencyDuration;
  List<String> _exceptionGroups = [];
  List<String> _customGroups = [];

  // Cache for emergency status checks
  static final Map<String, EmergencyStatusCache> _emergencyStatusCache = {};
  static Timer? _cacheCleanupTimer;

  bool get isEmergencyMode => _isEmergencyMode;
  DateTime? get emergencyStartTime => _emergencyStartTime;
  EmergencyDuration? get emergencyDuration => _emergencyDuration;
  List<String> get exceptionGroups => _exceptionGroups;
  List<String> get customGroups => _customGroups;

  // Initialize the service and load saved state
  Future<void> initialize() async {
    await _loadEmergencyState();
    _checkEmergencyExpiry();
  }

  // Load emergency state from SharedPreferences
  Future<void> _loadEmergencyState() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      _isEmergencyMode = prefs.getBool(_emergencyModeKey) ?? false;

      if (_isEmergencyMode) {
        final startTimeString = prefs.getString(_emergencyStartTimeKey);
        final durationIndex = prefs.getInt(_emergencyDurationKey);
        final exceptionGroupsJson = prefs.getString(_exceptionGroupsKey);
        final customGroupsJson = prefs.getString(_customGroupsKey);

        // Load exception groups
        if (exceptionGroupsJson != null) {
          final List<dynamic> groupsList = jsonDecode(exceptionGroupsJson);
          _exceptionGroups = groupsList.map((e) => e.toString()).toList();
        }

        // Load custom groups
        if (customGroupsJson != null) {
          final List<dynamic> groupsList = jsonDecode(customGroupsJson);
          _customGroups = groupsList.map((e) => e.toString()).toList();
        }

        if (startTimeString != null && durationIndex != null) {
          _emergencyStartTime = DateTime.parse(startTimeString);
          _emergencyDuration = EmergencyDuration.values[durationIndex];

          // Check if emergency mode has expired
          _checkEmergencyExpiry();
        } else {
          // Invalid data, clear emergency mode
          await _clearEmergencyMode();
        }
      }

      print('📱 Emergency mode loaded: $_isEmergencyMode');
    } catch (e) {
      print('❌ Error loading emergency state: $e');
      await _clearEmergencyMode();
    }
  }

  // Save emergency state to SharedPreferences
  Future<void> _saveEmergencyState() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setBool(_emergencyModeKey, _isEmergencyMode);

      if (_isEmergencyMode &&
          _emergencyStartTime != null &&
          _emergencyDuration != null) {
        await prefs.setString(
            _emergencyStartTimeKey, _emergencyStartTime!.toIso8601String());
        await prefs.setInt(_emergencyDurationKey, _emergencyDuration!.index);

        // Save exception groups
        await prefs.setString(
            _exceptionGroupsKey, jsonEncode(_exceptionGroups));

        // Save custom groups
        await prefs.setString(_customGroupsKey, jsonEncode(_customGroups));
      } else {
        await prefs.remove(_emergencyStartTimeKey);
        await prefs.remove(_emergencyDurationKey);
        await prefs.remove(_exceptionGroupsKey);
        await prefs.remove(_customGroupsKey);
      }

      print('📱 Emergency state saved: $_isEmergencyMode');
    } catch (e) {
      print('❌ Error saving emergency state: $e');
    }
  }

  // Activate emergency mode
  Future<void> activateEmergencyMode(EmergencyDuration duration,
      {List<String>? exceptionGroups}) async {
    _isEmergencyMode = true;
    _emergencyStartTime = DateTime.now();
    _emergencyDuration = duration;
    _exceptionGroups = exceptionGroups ?? [];

    await _saveEmergencyState();
    _emergencyModeController.add(true);

    // Clear ALL emergency status cache to force fresh checks
    clearAllCache();

    print(
        '🚨 Emergency mode activated for ${duration.displayName} - cleared all cache');

    // Sync with server so other users can see the emergency status
    await _syncEmergencyStatusToServer();

    // Schedule automatic deactivation
    _scheduleEmergencyExpiry();
  }

  // Deactivate emergency mode
  Future<void> deactivateEmergencyMode() async {
    await _clearEmergencyMode();
    _emergencyModeController.add(false);

    // Clear ALL emergency status cache to force fresh checks
    clearAllCache();

    // Sync with server to remove emergency status
    await _syncEmergencyStatusToServer();

    print('✅ Emergency mode deactivated - cleared all cache');
  }

  // Clear emergency mode data
  Future<void> _clearEmergencyMode() async {
    _isEmergencyMode = false;
    _emergencyStartTime = null;
    _emergencyDuration = null;
    _exceptionGroups = [];

    await _saveEmergencyState();
  }

  // Check if emergency mode has expired
  void _checkEmergencyExpiry() {
    if (_isEmergencyMode &&
        _emergencyStartTime != null &&
        _emergencyDuration != null) {
      final expiryTime = _emergencyStartTime!.add(_emergencyDuration!.duration);

      if (DateTime.now().isAfter(expiryTime)) {
        print('⏰ Emergency mode expired, deactivating...');
        deactivateEmergencyMode();
      } else {
        // Schedule expiry check
        _scheduleEmergencyExpiry();
      }
    }
  }

  // Schedule automatic emergency mode expiry
  void _scheduleEmergencyExpiry() {
    if (_emergencyStartTime != null && _emergencyDuration != null) {
      final expiryTime = _emergencyStartTime!.add(_emergencyDuration!.duration);
      final timeUntilExpiry = expiryTime.difference(DateTime.now());

      if (timeUntilExpiry.isNegative) {
        deactivateEmergencyMode();
      } else {
        Timer(timeUntilExpiry, () {
          deactivateEmergencyMode();
        });

        print(
            '⏰ Emergency mode will expire in ${timeUntilExpiry.inMinutes} minutes');
      }
    }
  }

  // Get remaining time in emergency mode
  Duration? getRemainingTime() {
    if (_isEmergencyMode &&
        _emergencyStartTime != null &&
        _emergencyDuration != null) {
      final expiryTime = _emergencyStartTime!.add(_emergencyDuration!.duration);
      final remaining = expiryTime.difference(DateTime.now());

      return remaining.isNegative ? Duration.zero : remaining;
    }
    return null;
  }

  // Get formatted remaining time string
  String getFormattedRemainingTime([dynamic localizations]) {
    final remaining = getRemainingTime();
    if (remaining == null) return '';

    if (localizations != null) {
      // Use localized strings
      if (remaining.inDays > 0) {
        return localizations.daysRemaining(
            remaining.inDays, remaining.inHours % 24);
      } else if (remaining.inHours > 0) {
        return localizations.hoursRemaining(
            remaining.inHours, remaining.inMinutes % 60);
      } else {
        return localizations.minutesRemaining(remaining.inMinutes);
      }
    } else {
      // Fallback to English
      if (remaining.inDays > 0) {
        return '${remaining.inDays}d ${remaining.inHours % 24}h remaining';
      } else if (remaining.inHours > 0) {
        return '${remaining.inHours}h ${remaining.inMinutes % 60}m remaining';
      } else {
        return '${remaining.inMinutes}m remaining';
      }
    }
  }

  // Get localized formatted remaining time string
  String getLocalizedFormattedRemainingTime(BuildContext context) {
    final remaining = getRemainingTime();
    if (remaining == null) return '';

    if (remaining.inDays > 0) {
      final days = remaining.inDays;
      final hours = remaining.inHours % 24;
      if (hours > 0) {
        return '$days يوم $hours ساعة متبقية';
      } else {
        return '$days يوم متبقي';
      }
    } else if (remaining.inHours > 0) {
      final hours = remaining.inHours;
      final minutes = remaining.inMinutes % 60;
      if (minutes > 0) {
        return '$hours ساعة $minutes دقيقة متبقية';
      } else {
        return '$hours ساعة متبقية';
      }
    } else {
      final minutes = remaining.inMinutes;
      return '$minutes دقيقة متبقية';
    }
  }

  // Get emergency message for dialogs
  String getEmergencyMessage([dynamic localizations]) {
    final remaining = getFormattedRemainingTime(localizations);

    if (localizations != null) {
      // Use localized strings
      final groupsText = _exceptionGroups.isNotEmpty
          ? '🔸 المجموعات المستثناة: ${_exceptionGroups.map((group) => getLocalizedGroupName(group, localizations)).join('، ')}'
          : '⚠️ لا توجد مجموعات استثناء';

      // Get current user's name from profile or use default
      final userName = 'المستخدم'; // Default fallback

      return localizations.emergencyModeMessage(
          userName, groupsText, remaining);
    } else {
      // Fallback to English
      final groupsText = _exceptionGroups.isNotEmpty
          ? 'Exception groups: ${_exceptionGroups.join(', ')}'
          : 'No exception groups';

      return 'This user is currently in emergency mode. '
          'They are not available for calls or messages. '
          '$groupsText. '
          '${remaining.isNotEmpty ? 'Emergency mode: $remaining' : ''}';
    }
  }

  // Sync emergency status to server
  Future<void> _syncEmergencyStatusToServer() async {
    try {
      final connectivityService = ConnectivityService();
      if (!connectivityService.isOnline) {
        print('📱 Offline - emergency status will sync when online');
        return;
      }

      final currentUser = SupabaseService.currentUser;
      if (currentUser == null) {
        print('❌ No current user for emergency status sync');
        return;
      }

      if (_isEmergencyMode &&
          _emergencyStartTime != null &&
          _emergencyDuration != null) {
        // Calculate expiry time
        final expiryTime =
            _emergencyStartTime!.add(_emergencyDuration!.duration);

        // Update user profile with emergency status
        await SupabaseService.client.from('profiles').update({
          'emergency_mode': true,
          'emergency_start_time': _emergencyStartTime!.toIso8601String(),
          'emergency_expiry_time': expiryTime.toIso8601String(),
          'exception_groups': _exceptionGroups,
        }).eq('id', currentUser.id);

        print('✅ Emergency status synced to server');
      } else {
        // Clear emergency status
        await SupabaseService.client.from('profiles').update({
          'emergency_mode': false,
          'emergency_start_time': null,
          'emergency_expiry_time': null,
          'exception_groups': [],
        }).eq('id', currentUser.id);

        print('✅ Emergency status cleared on server');
      }
    } catch (e) {
      print('❌ Failed to sync emergency status to server: $e');
    }
  }

  // Check if a specific user is in emergency mode (for other users to check)
  static Future<EmergencyStatus?> checkUserEmergencyStatus(
      String userId) async {
    try {
      // Check cache first
      final cachedEntry = _emergencyStatusCache[userId];
      if (cachedEntry != null && !cachedEntry.isExpired) {
        print(
            '📱 Using cached emergency status for user $userId: ${cachedEntry.status?.isActive ?? false}');
        return cachedEntry.status;
      }

      print('🔄 Fetching fresh emergency status for user $userId from server');

      final connectivityService = ConnectivityService();
      if (!connectivityService.isOnline) {
        print('📱 Offline - cannot check emergency status');
        // Return cached data even if expired when offline
        return cachedEntry?.status;
      }

      final response = await SupabaseService.client
          .from('profiles')
          .select(
              'emergency_mode, emergency_start_time, emergency_expiry_time, exception_groups, full_name')
          .eq('id', userId)
          .single();

      EmergencyStatus? status;
      if (response['emergency_mode'] == true) {
        final expiryTimeString = response['emergency_expiry_time'] as String?;
        if (expiryTimeString != null) {
          // Parse timestamps as naive datetime (no timezone conversion)
          // Remove timezone info and parse as local time to avoid conversion
          final cleanExpiryString =
              expiryTimeString.replaceAll(RegExp(r'[+\-]\d{2}:\d{2}$|Z$'), '');
          final expiryTime = DateTime.parse(cleanExpiryString);

          // Check if emergency mode has expired using local times (no timezone conversion)
          if (DateTime.now().isAfter(expiryTime)) {
            status = null;
          } else {
            // Parse exception groups from JSON
            final exceptionGroupsJson =
                response['exception_groups'] as List<dynamic>? ?? [];
            final exceptionGroups =
                exceptionGroupsJson.map((e) => e.toString()).toList();

            final startTimeString = response['emergency_start_time'] as String;
            final cleanStartString =
                startTimeString.replaceAll(RegExp(r'[+\-]\d{2}:\d{2}$|Z$'), '');
            final startTime = DateTime.parse(cleanStartString);

            status = EmergencyStatus(
              isActive: true,
              startTime: startTime,
              expiryTime: expiryTime,
              exceptionGroups: exceptionGroups,
              userName: response['full_name'] as String? ?? 'User',
            );
          }
        }
      }

      // Cache the result
      _emergencyStatusCache[userId] = EmergencyStatusCache(
        status: status,
        cachedAt: DateTime.now(),
      );

      print(
          '✅ Emergency status for user $userId: ${status?.isActive ?? false}');
      if (status?.isActive == true) {
        print(
            '🚨 User $userId is in emergency mode with groups: ${status?.exceptionGroups}');
      }

      return status;
    } catch (e) {
      print('❌ Failed to check user emergency status: $e');
      // Return cached data if available
      final cachedEntry = _emergencyStatusCache[userId];
      return cachedEntry?.status;
    }
  }

  // Get emergency message for a specific user (localized)
  static String getEmergencyMessageForUser(EmergencyStatus status,
      [dynamic localizations]) {
    // Use local times without timezone conversion to match stored data
    final now = DateTime.now();
    final remaining = status.expiryTime.difference(now);
    String remainingText = '';

    if (localizations != null) {
      // Use localized strings
      if (remaining.inDays > 0) {
        remainingText = localizations.daysRemaining(
            remaining.inDays, remaining.inHours % 24);
      } else if (remaining.inHours > 0) {
        remainingText = localizations.hoursRemaining(
            remaining.inHours, remaining.inMinutes % 60);
      } else {
        remainingText = localizations.minutesRemaining(remaining.inMinutes);
      }

      final groupsText = status.exceptionGroups.isNotEmpty
          ? '🔸 المجموعات المستثناة: ${status.exceptionGroups.map((group) => getLocalizedGroupName(group, localizations)).join('، ')}'
          : '⚠️ لا توجد مجموعات استثناء';
      return localizations.emergencyModeMessage(
          status.userName, groupsText, remainingText);
    } else {
      // Fallback to English
      if (remaining.inDays > 0) {
        remainingText =
            '${remaining.inDays}d ${remaining.inHours % 24}h remaining';
      } else if (remaining.inHours > 0) {
        remainingText =
            '${remaining.inHours}h ${remaining.inMinutes % 60}m remaining';
      } else {
        remainingText = '${remaining.inMinutes}m remaining';
      }

      final groupsText = status.exceptionGroups.isNotEmpty
          ? 'Exception groups: ${status.exceptionGroups.join(', ')}'
          : 'No exception groups';
      return '${status.userName} is currently in emergency mode. '
          'They are not available for calls or messages. '
          '$groupsText. Emergency mode: $remainingText';
    }
  }

  // Helper method to get localized group names
  static String getLocalizedGroupName(String group, dynamic localizations) {
    switch (group) {
      case 'family':
        return localizations.family;
      case 'coworkers':
        return localizations.coworkers;
      case 'friends':
        return localizations.friends;
      default:
        return group; // Custom group names are returned as-is
    }
  }

  // Get formatted remaining time for a specific user's emergency status
  static String getFormattedRemainingTimeForUser(
      EmergencyStatus status, dynamic localizations) {
    // Use local times without timezone conversion to match stored data
    final now = DateTime.now();
    final remaining = status.expiryTime.difference(now);

    if (remaining.isNegative) return '';

    if (localizations != null) {
      // Use localized strings
      if (remaining.inDays > 0) {
        return localizations.daysRemaining(
            remaining.inDays, remaining.inHours % 24);
      } else if (remaining.inHours > 0) {
        return localizations.hoursRemaining(
            remaining.inHours, remaining.inMinutes % 60);
      } else {
        return localizations.minutesRemaining(remaining.inMinutes);
      }
    } else {
      // Fallback to English
      if (remaining.inDays > 0) {
        return '${remaining.inDays}d ${remaining.inHours % 24}h remaining';
      } else if (remaining.inHours > 0) {
        return '${remaining.inHours}h ${remaining.inMinutes % 60}m remaining';
      } else {
        return '${remaining.inMinutes}m remaining';
      }
    }
  }

  // Clear emergency status cache for a specific user
  static void clearCacheForUser(String userId) {
    _emergencyStatusCache.remove(userId);
    print('🗑️ Cleared emergency status cache for user $userId');
  }

  // Clear all emergency status cache
  static void clearAllCache() {
    _emergencyStatusCache.clear();
    print('🗑️ Cleared all emergency status cache');
  }

  // Clear all emergency data during sign out
  Future<void> clearAllEmergencyData() async {
    try {
      print('🧹 Clearing all emergency data during sign out...');

      // Clear current user's emergency mode
      await _clearEmergencyMode();

      // Clear all emergency status cache
      clearAllCache();

      print('✅ All emergency data cleared successfully');
    } catch (e) {
      print('❌ Error clearing emergency data: $e');
    }
  }

  // Refresh emergency status cache when connectivity is restored
  static Future<void> refreshCacheOnConnectivity() async {
    try {
      final connectivityService = ConnectivityService();
      if (!connectivityService.isOnline) {
        print('📱 Still offline - cannot refresh emergency cache');
        return;
      }

      print('🔄 Refreshing emergency status cache...');

      // Get all cached user IDs
      final userIds = _emergencyStatusCache.keys.toList();

      // Refresh each cached user's status
      for (final userId in userIds) {
        // Remove from cache to force fresh fetch
        _emergencyStatusCache.remove(userId);
        // Fetch fresh data (this will cache it again)
        await checkUserEmergencyStatus(userId);
      }

      print('✅ Emergency status cache refreshed for ${userIds.length} users');
    } catch (e) {
      print('❌ Failed to refresh emergency status cache: $e');
    }
  }

  // Start cache cleanup timer
  static void _startCacheCleanup() {
    _cacheCleanupTimer?.cancel();
    _cacheCleanupTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      final expiredKeys = <String>[];

      for (final entry in _emergencyStatusCache.entries) {
        if (entry.value.isExpired) {
          expiredKeys.add(entry.key);
        }
      }

      for (final key in expiredKeys) {
        _emergencyStatusCache.remove(key);
      }

      if (expiredKeys.isNotEmpty) {
        print(
            '🗑️ Cleaned up ${expiredKeys.length} expired emergency status cache entries');
      }
    });
  }

  // Dispose resources
  // Group management methods
  List<String> getAllAvailableGroups() {
    final allGroups = <String>[];
    allGroups.addAll(defaultGroups);
    allGroups.addAll(_customGroups);
    return allGroups;
  }

  Future<void> addCustomGroup(String groupName) async {
    if (!_customGroups.contains(groupName) &&
        !defaultGroups.contains(groupName)) {
      _customGroups.add(groupName);
      await _saveEmergencyState();
      await _syncCustomGroupsToServer();
    }
  }

  Future<void> removeCustomGroup(String groupName) async {
    if (_customGroups.contains(groupName)) {
      _customGroups.remove(groupName);
      // Also remove from exception groups if it was selected
      _exceptionGroups.remove(groupName);
      await _saveEmergencyState();
      await _syncCustomGroupsToServer();
    }
  }

  // Sync custom groups to server
  Future<void> _syncCustomGroupsToServer() async {
    try {
      final connectivityService = ConnectivityService();
      if (!connectivityService.isOnline) {
        return; // Will sync when back online
      }

      final currentUser = SupabaseService.currentUser;
      if (currentUser == null) return;

      // Store custom groups in the exception_groups field along with selected groups
      // We'll use a special format to distinguish custom group definitions from selected groups
      // For now, just store locally until we decide on the database schema
      print('📱 Custom groups stored locally: $_customGroups');
    } catch (e) {
      // Silently fail - will retry when emergency mode is activated
    }
  }

  void dispose() {
    _emergencyModeController.close();
    _cacheCleanupTimer?.cancel();
  }
}

// Emergency status data class
class EmergencyStatus {
  final bool isActive;
  final DateTime startTime;
  final DateTime expiryTime;
  final List<String> exceptionGroups; // Groups that can still contact the user
  final String userName;

  EmergencyStatus({
    required this.isActive,
    required this.startTime,
    required this.expiryTime,
    required this.exceptionGroups,
    required this.userName,
  });
}
