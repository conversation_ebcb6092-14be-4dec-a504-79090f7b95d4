import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'supabase_service.dart';
import 'connectivity_service.dart';

enum EmergencyDuration {
  oneHour,
  threeHours,
  sixHours,
  twelveHours,
  oneDay,
  threeDays,
  oneWeek,
}

extension EmergencyDurationExtension on EmergencyDuration {
  String get displayName {
    switch (this) {
      case EmergencyDuration.oneHour:
        return '1 Hour';
      case EmergencyDuration.threeHours:
        return '3 Hours';
      case EmergencyDuration.sixHours:
        return '6 Hours';
      case EmergencyDuration.twelveHours:
        return '12 Hours';
      case EmergencyDuration.oneDay:
        return '1 Day';
      case EmergencyDuration.threeDays:
        return '3 Days';
      case EmergencyDuration.oneWeek:
        return '1 Week';
    }
  }

  Duration get duration {
    switch (this) {
      case EmergencyDuration.oneHour:
        return const Duration(hours: 1);
      case EmergencyDuration.threeHours:
        return const Duration(hours: 3);
      case EmergencyDuration.sixHours:
        return const Duration(hours: 6);
      case EmergencyDuration.twelveHours:
        return const Duration(hours: 12);
      case EmergencyDuration.oneDay:
        return const Duration(days: 1);
      case EmergencyDuration.threeDays:
        return const Duration(days: 3);
      case EmergencyDuration.oneWeek:
        return const Duration(days: 7);
    }
  }

  String get icon {
    switch (this) {
      case EmergencyDuration.oneHour:
      case EmergencyDuration.threeHours:
      case EmergencyDuration.sixHours:
      case EmergencyDuration.twelveHours:
        return '⏰';
      case EmergencyDuration.oneDay:
      case EmergencyDuration.threeDays:
      case EmergencyDuration.oneWeek:
        return '📅';
    }
  }
}

class EmergencyModeService {
  static final EmergencyModeService _instance = EmergencyModeService._internal();
  factory EmergencyModeService() => _instance;
  EmergencyModeService._internal();

  static const String _emergencyModeKey = 'emergency_mode_active';
  static const String _emergencyStartTimeKey = 'emergency_start_time';
  static const String _emergencyDurationKey = 'emergency_duration';
  static const String _emergencyReasonKey = 'emergency_reason';

  final StreamController<bool> _emergencyModeController = StreamController<bool>.broadcast();
  Stream<bool> get emergencyModeStream => _emergencyModeController.stream;

  bool _isEmergencyMode = false;
  DateTime? _emergencyStartTime;
  EmergencyDuration? _emergencyDuration;
  String? _emergencyReason;

  bool get isEmergencyMode => _isEmergencyMode;
  DateTime? get emergencyStartTime => _emergencyStartTime;
  EmergencyDuration? get emergencyDuration => _emergencyDuration;
  String? get emergencyReason => _emergencyReason;

  // Initialize the service and load saved state
  Future<void> initialize() async {
    await _loadEmergencyState();
    _checkEmergencyExpiry();
  }

  // Load emergency state from SharedPreferences
  Future<void> _loadEmergencyState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      _isEmergencyMode = prefs.getBool(_emergencyModeKey) ?? false;
      
      if (_isEmergencyMode) {
        final startTimeString = prefs.getString(_emergencyStartTimeKey);
        final durationIndex = prefs.getInt(_emergencyDurationKey);
        _emergencyReason = prefs.getString(_emergencyReasonKey);
        
        if (startTimeString != null && durationIndex != null) {
          _emergencyStartTime = DateTime.parse(startTimeString);
          _emergencyDuration = EmergencyDuration.values[durationIndex];
          
          // Check if emergency mode has expired
          _checkEmergencyExpiry();
        } else {
          // Invalid data, clear emergency mode
          await _clearEmergencyMode();
        }
      }
      
      print('📱 Emergency mode loaded: $_isEmergencyMode');
    } catch (e) {
      print('❌ Error loading emergency state: $e');
      await _clearEmergencyMode();
    }
  }

  // Save emergency state to SharedPreferences
  Future<void> _saveEmergencyState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setBool(_emergencyModeKey, _isEmergencyMode);
      
      if (_isEmergencyMode && _emergencyStartTime != null && _emergencyDuration != null) {
        await prefs.setString(_emergencyStartTimeKey, _emergencyStartTime!.toIso8601String());
        await prefs.setInt(_emergencyDurationKey, _emergencyDuration!.index);
        if (_emergencyReason != null) {
          await prefs.setString(_emergencyReasonKey, _emergencyReason!);
        }
      } else {
        await prefs.remove(_emergencyStartTimeKey);
        await prefs.remove(_emergencyDurationKey);
        await prefs.remove(_emergencyReasonKey);
      }
      
      print('📱 Emergency state saved: $_isEmergencyMode');
    } catch (e) {
      print('❌ Error saving emergency state: $e');
    }
  }

  // Activate emergency mode
  Future<void> activateEmergencyMode(EmergencyDuration duration, {String? reason}) async {
    _isEmergencyMode = true;
    _emergencyStartTime = DateTime.now();
    _emergencyDuration = duration;
    _emergencyReason = reason;

    await _saveEmergencyState();
    _emergencyModeController.add(true);

    print('🚨 Emergency mode activated for ${duration.displayName}');

    // Sync with server so other users can see the emergency status
    await _syncEmergencyStatusToServer();

    // Schedule automatic deactivation
    _scheduleEmergencyExpiry();
  }

  // Deactivate emergency mode
  Future<void> deactivateEmergencyMode() async {
    await _clearEmergencyMode();
    _emergencyModeController.add(false);

    // Sync with server to remove emergency status
    await _syncEmergencyStatusToServer();

    print('✅ Emergency mode deactivated');
  }

  // Clear emergency mode data
  Future<void> _clearEmergencyMode() async {
    _isEmergencyMode = false;
    _emergencyStartTime = null;
    _emergencyDuration = null;
    _emergencyReason = null;
    
    await _saveEmergencyState();
  }

  // Check if emergency mode has expired
  void _checkEmergencyExpiry() {
    if (_isEmergencyMode && _emergencyStartTime != null && _emergencyDuration != null) {
      final expiryTime = _emergencyStartTime!.add(_emergencyDuration!.duration);
      
      if (DateTime.now().isAfter(expiryTime)) {
        print('⏰ Emergency mode expired, deactivating...');
        deactivateEmergencyMode();
      } else {
        // Schedule expiry check
        _scheduleEmergencyExpiry();
      }
    }
  }

  // Schedule automatic emergency mode expiry
  void _scheduleEmergencyExpiry() {
    if (_emergencyStartTime != null && _emergencyDuration != null) {
      final expiryTime = _emergencyStartTime!.add(_emergencyDuration!.duration);
      final timeUntilExpiry = expiryTime.difference(DateTime.now());
      
      if (timeUntilExpiry.isNegative) {
        deactivateEmergencyMode();
      } else {
        Timer(timeUntilExpiry, () {
          deactivateEmergencyMode();
        });
        
        print('⏰ Emergency mode will expire in ${timeUntilExpiry.inMinutes} minutes');
      }
    }
  }

  // Get remaining time in emergency mode
  Duration? getRemainingTime() {
    if (_isEmergencyMode && _emergencyStartTime != null && _emergencyDuration != null) {
      final expiryTime = _emergencyStartTime!.add(_emergencyDuration!.duration);
      final remaining = expiryTime.difference(DateTime.now());
      
      return remaining.isNegative ? Duration.zero : remaining;
    }
    return null;
  }

  // Get formatted remaining time string
  String getFormattedRemainingTime() {
    final remaining = getRemainingTime();
    if (remaining == null) return '';
    
    if (remaining.inDays > 0) {
      return '${remaining.inDays}d ${remaining.inHours % 24}h remaining';
    } else if (remaining.inHours > 0) {
      return '${remaining.inHours}h ${remaining.inMinutes % 60}m remaining';
    } else {
      return '${remaining.inMinutes}m remaining';
    }
  }

  // Get emergency message for dialogs
  String getEmergencyMessage() {
    final remaining = getFormattedRemainingTime();
    final reason = _emergencyReason?.isNotEmpty == true ? _emergencyReason! : 'personal emergency';
    
    return 'This user is currently in emergency mode due to $reason. '
           'They are not available for calls or messages. '
           '${remaining.isNotEmpty ? 'Emergency mode: $remaining' : ''}';
  }

  // Sync emergency status to server
  Future<void> _syncEmergencyStatusToServer() async {
    try {
      final connectivityService = ConnectivityService();
      if (!connectivityService.isOnline) {
        print('📱 Offline - emergency status will sync when online');
        return;
      }

      final currentUser = SupabaseService.currentUser;
      if (currentUser == null) {
        print('❌ No current user for emergency status sync');
        return;
      }

      if (_isEmergencyMode && _emergencyStartTime != null && _emergencyDuration != null) {
        // Calculate expiry time
        final expiryTime = _emergencyStartTime!.add(_emergencyDuration!.duration);

        // Update user profile with emergency status
        await SupabaseService.client.from('profiles').update({
          'emergency_mode': true,
          'emergency_start_time': _emergencyStartTime!.toIso8601String(),
          'emergency_expiry_time': expiryTime.toIso8601String(),
          'emergency_reason': _emergencyReason ?? 'personal emergency',
        }).eq('id', currentUser.id);

        print('✅ Emergency status synced to server');
      } else {
        // Clear emergency status
        await SupabaseService.client.from('profiles').update({
          'emergency_mode': false,
          'emergency_start_time': null,
          'emergency_expiry_time': null,
          'emergency_reason': null,
        }).eq('id', currentUser.id);

        print('✅ Emergency status cleared on server');
      }
    } catch (e) {
      print('❌ Failed to sync emergency status to server: $e');
    }
  }

  // Check if a specific user is in emergency mode (for other users to check)
  static Future<EmergencyStatus?> checkUserEmergencyStatus(String userId) async {
    try {
      final connectivityService = ConnectivityService();
      if (!connectivityService.isOnline) {
        print('📱 Offline - cannot check emergency status');
        return null;
      }

      final response = await SupabaseService.client
          .from('profiles')
          .select('emergency_mode, emergency_start_time, emergency_expiry_time, emergency_reason, full_name')
          .eq('id', userId)
          .single();

      if (response['emergency_mode'] == true) {
        final expiryTimeString = response['emergency_expiry_time'] as String?;
        if (expiryTimeString != null) {
          final expiryTime = DateTime.parse(expiryTimeString);

          // Check if emergency mode has expired
          if (DateTime.now().isAfter(expiryTime)) {
            print('⏰ User emergency mode has expired');
            return null;
          }

          return EmergencyStatus(
            isActive: true,
            startTime: DateTime.parse(response['emergency_start_time'] as String),
            expiryTime: expiryTime,
            reason: response['emergency_reason'] as String? ?? 'personal emergency',
            userName: response['full_name'] as String? ?? 'User',
          );
        }
      }

      return null;
    } catch (e) {
      print('❌ Failed to check user emergency status: $e');
      return null;
    }
  }

  // Get emergency message for a specific user
  static String getEmergencyMessageForUser(EmergencyStatus status) {
    final remaining = status.expiryTime.difference(DateTime.now());
    String remainingText = '';

    if (remaining.inDays > 0) {
      remainingText = '${remaining.inDays}d ${remaining.inHours % 24}h remaining';
    } else if (remaining.inHours > 0) {
      remainingText = '${remaining.inHours}h ${remaining.inMinutes % 60}m remaining';
    } else {
      remainingText = '${remaining.inMinutes}m remaining';
    }

    return '${status.userName} is currently in emergency mode due to ${status.reason}. '
           'They are not available for calls or messages. '
           'Emergency mode: $remainingText';
  }

  // Dispose resources
  void dispose() {
    _emergencyModeController.close();
  }
}

// Emergency status data class
class EmergencyStatus {
  final bool isActive;
  final DateTime startTime;
  final DateTime expiryTime;
  final String reason;
  final String userName;

  EmergencyStatus({
    required this.isActive,
    required this.startTime,
    required this.expiryTime,
    required this.reason,
    required this.userName,
  });
}
