{"@@locale": "en", "appTitle": "Contact Times", "@appTitle": {"description": "The title of the application"}, "appSubtitle": "Manage when you can be contacted", "@appSubtitle": {"description": "The subtitle of the application"}, "signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "fullName": "Full Name", "phoneNumber": "Phone Number", "username": "Username", "pleaseEnterEmail": "Please enter your email", "pleaseEnterValidEmail": "Please enter a valid email", "pleaseEnterPassword": "Please enter your password", "pleaseEnterFullName": "Please enter your full name", "pleaseEnterPhoneNumber": "Please enter your phone number", "pleaseEnterUsername": "Please enter your username", "passwordsDoNotMatch": "Passwords do not match", "signInFailed": "Sign in failed", "signUpFailed": "Sign up failed", "profileUpdateFailed": "Failed to update profile", "profileUpdatedSuccessfully": "Profile updated successfully", "dontHaveAccount": "Don't have an account? Sign Up", "alreadyHaveAccount": "Already have an account? Sign In", "contacts": "Contacts", "categories": "Categories", "distribute": "Distribute", "profile": "Profile", "dashboard": "Dashboard", "loadingContacts": "Loading contacts...", "noContactsFound": "No contacts found", "contactsPermissionRequired": "Contacts permission is required to sync your contacts", "grantPermission": "Grant Permission", "syncContacts": "Sync Contacts", "availableTimes": "Available Times", "timeSlots": "Time Slots", "addTimeSlot": "Add Time Slot", "editTimeSlots": "Edit Time Slots", "saveTimeSlots": "Save Time Slots", "startTime": "Start Time", "endTime": "End Time", "selectDay": "Select Day", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "activeNow": "ACTIVE NOW", "notify": "Notify", "call": "Call", "message": "Message", "profileInformation": "Profile Information", "appInformation": "App Information", "version": "Version", "buildNumber": "Build Number", "notificationSettings": "Notification Settings", "changePhoto": "Change Photo", "updateProfile": "Update Profile", "aboutContactTimes": "About Contact Times", "manageWhenYouCanBeContacted": "Manage when you can be contacted by different people. Set up categories and time slots to let others know your availability preferences.", "loadingProfile": "Loading Profile...", "profileNotFound": "Profile not found", "profilePictureUploadComingSoon": "Profile picture upload coming soon", "noPhoneNumber": "No phone number", "welcome": "Welcome!", "failedToSignOut": "Failed to sign out", "uploadProfilePicture": "Upload Profile Picture", "selectImageSource": "Select Image Source", "camera": "Camera", "gallery": "Gallery", "imageUploadedSuccessfully": "Profile picture updated successfully", "failedToUploadImage": "Failed to upload image", "deleteAccount": "Delete Account", "deleteAccountConfirmation": "Are you sure you want to delete your account? This action cannot be undone.", "deleteAccountWarning": "This will permanently delete your account and all associated data.", "confirmDelete": "Confirm Delete", "accountDeletedSuccessfully": "Account deleted successfully", "failedToDeleteAccount": "Failed to delete account", "enterYourFullName": "Enter your full name", "enterYourUsername": "Enter your username", "enterYourPhoneNumber": "Enter your phone number", "language": "Language", "selectLanguage": "Select Language", "english": "English", "arabic": "العربية", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "done": "Done", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "offline": "Offline", "online": "Online", "syncing": "Syncing...", "syncComplete": "Sync Complete", "noTimeSlots": "No time slots configured", "addFirstTimeSlot": "Add your first time slot", "timeSlotOverlap": "Time slots overlap and will be merged", "categoryAssigned": "Category assigned", "categoryUpdated": "Category updated", "contactCategorized": "Contact categorized successfully", "loading": "Loading...", "retry": "Retry", "refresh": "Refresh", "myCategories": "My Categories", "pending": "Pending", "loadingYourCategories": "Loading your categories...", "oopsSomethingWentWrong": "Oops! Something went wrong", "tryAgain": "Try Again", "categoryNoteUpdatedSuccessfully": "Category note updated successfully", "updatedLocally": "Updated locally. Will sync when online.", "updatedOffline": "Updated offline. Will sync when online.", "failedToUpdateCategory": "Failed to update category", "timeSlotsUpdatedSuccessfully": "Time slots updated successfully", "allChangesSyncedSuccessfully": "All changes synced successfully", "noDescriptionAddedYet": "No description added yet...", "todaysTimeSlots": "Today's Time Slots ({count})", "@todaysTimeSlots": {"placeholders": {"count": {"type": "int"}}}, "timeSlotsCount": "Time Slots ({count})", "@timeSlotsCount": {"placeholders": {"count": {"type": "int"}}}, "perfectTimeToContact": "Perfect time to contact!", "andMoreSlotsToday": "... and {count} more slots today", "@andMoreSlotsToday": {"placeholders": {"count": {"type": "int"}}}, "noTimeSlotsForToday": "No time slots for today. You have {count} slot{plural} on other days.", "@noTimeSlotsForToday": {"placeholders": {"count": {"type": "int"}, "plural": {"type": "String"}}}, "noTimeSlotsConfiguredYet": "No time slots configured yet", "editCategory": "Edit {categoryName}", "@editCategory": {"placeholders": {"categoryName": {"type": "String"}}}, "description": "Description", "addDescriptionForCategory": "Add a description for this category...", "timeSlotNumber": "Time Slot {number}", "@timeSlotNumber": {"placeholders": {"number": {"type": "int"}}}, "dayOfWeek": "Day of Week", "noTimeSlotsYet": "No Time Slots Yet", "addYourFirstTimeSlot": "Add your first time slot to get started.\nLet others know when you're available!", "addYourFirstTimeSlotButton": "Add Your First Time Slot", "timeSlotOverlapWarning": "This slot overlaps with others and will be merged when saved", "saving": "Saving...", "saveChanges": "Save Changes", "pleaseAddAtLeastOneTimeSlot": "Please add at least one time slot", "mergedOverlappingTimeSlots": "Merged {count} overlapping time slot{plural}", "@mergedOverlappingTimeSlots": {"placeholders": {"count": {"type": "int"}, "plural": {"type": "String"}}}, "failedToSaveTimeSlots": "Failed to save time slots: {error}", "@failedToSaveTimeSlots": {"placeholders": {"error": {"type": "String"}}}, "timeSlotsCountSimple": "{count} time slot{plural}", "@timeSlotsCountSimple": {"placeholders": {"count": {"type": "int"}, "plural": {"type": "String"}}}, "hint": "Hint", "enterYourFullNameHint": "Enter your full name", "enterYourUsernameHint": "Enter your username", "enterYourPhoneNumberHint": "Enter your phone number", "contactsPermissionDenied": "Contacts permission denied", "contactsSynced": "Contacts synced successfully", "failedToSyncContacts": "Failed to sync contacts", "searchContacts": "Search contacts...", "noContactsAvailable": "No contacts available", "tapToAssignCategory": "Tap to assign category", "categoryAssignedTo": "Category assigned to {contactName}", "@categoryAssignedTo": {"placeholders": {"contactName": {"type": "String"}}}, "timeSlot": "Time Slot", "noTimeSlotsConfigured": "No time slots configured", "overlappingTimeSlots": "Overlapping time slots will be merged", "invalidTimeSlot": "Invalid time slot", "startTimeMustBeBeforeEndTime": "Start time must be before end time", "invalidTimeSlotRange": "Invalid time range: Start time ({startTime}) must be before end time ({endTime})", "@invalidTimeSlotRange": {"placeholders": {"startTime": {"type": "String"}, "endTime": {"type": "String"}}}, "distributeContacts": "Distribute Contacts", "assignContactsToCategories": "Assign contacts to categories", "swipeToAssign": "Swipe to assign category", "skipContact": "Skip Contact", "assignToCategory": "Assign to Category", "organizeYourContactsIntoCategories": "Organize your contacts into categories", "contactsToCategorizePlural": "{count} contacts to categorize", "contactAssignedToCategory": "{contactName} assigned to \"{categoryName}\"", "contactAllNumbersAssignedToCategory": "{contactName} (all {phoneCount} numbers) assigned to {categoryName}", "contactPartialNumbersAssignedToCategory": "{contactName} ({successCount} of {phoneCount} numbers) assigned to {categoryName}", "refreshContacts": "Refresh contacts", "allDone": "All Done! 🎉", "allYourContactsHaveBeenCategorized": "All your contacts have been categorized!", "greatJobOrganizingYourContacts": "Great job organizing your contacts.", "allContactsCategorized": "All contacts categorized!", "categoryDescriptions": "Category Descriptions", "assigningContact": "Assigning Contact", "assigningAllPhoneNumbers": "Assigning all {count} phone numbers for {contactName} to {categoryName}...", "@assigningAllPhoneNumbers": {"placeholders": {"count": {"type": "int"}, "contactName": {"type": "String"}, "categoryName": {"type": "String"}}}, "partialAssignment": "Partial Assignment", "successfullyAssignedPhoneNumbers": "Successfully assigned {successCount} phone numbers for {contactName}, but {failedCount} failed. The contact has been moved from the uncategorized list.", "@successfullyAssignedPhoneNumbers": {"placeholders": {"successCount": {"type": "int"}, "contactName": {"type": "String"}, "failedCount": {"type": "int"}}}, "retryFailed": "Retry Failed", "connectionError": "Connection Error", "pleaseCheckYourInternetConnection": "Please check your internet connection and try again.", "requestTimeout": "Request Timeout", "requestTookTooLong": "The request took too long. Please try again.", "assignmentFailed": "Assignment Failed", "failedToAssignContactToCategory": "Failed to assign {contactName} to {categoryName}. Please try again.", "@failedToAssignContactToCategory": {"placeholders": {"contactName": {"type": "String"}, "categoryName": {"type": "String"}}}, "ok": "OK", "noInternetConnection": "No Internet Connection", "noInternetMessage": "Please check your internet connection and try again", "checkConnection": "Check Connection", "aboutApp": "About App", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "notifications": "Notifications", "enableNotifications": "Enable Notifications", "disableNotifications": "Disable Notifications", "notificationPermissionRequired": "Notification permission is required", "notificationsEnabled": "Notifications enabled", "notificationsDisabled": "Notifications disabled", "calling": "Calling", "callContact": "Call Contact", "sendMessage": "Send Message", "contactInfo": "Contact Info", "noContactInfo": "No contact information available", "editCategories": "Edit Categories", "categorySettings": "Category Settings", "categoryDescription": "Category Description", "addCategoryDescription": "Add a description for this category", "noCategoriesFound": "No categories found", "settings": "Settings", "preferences": "Preferences", "account": "Account", "security": "Security", "help": "Help", "support": "Support", "feedback": "<PERSON><PERSON><PERSON>", "confirm": "Confirm", "yes": "Yes", "no": "No", "apply": "Apply", "reset": "Reset", "clear": "Clear", "search": "Search", "filter": "Filter", "sort": "Sort", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "thisWeek": "This Week", "lastWeek": "Last Week", "nextWeek": "Next Week", "morning": "Morning", "afternoon": "Afternoon", "evening": "Evening", "night": "Night", "am": "AM", "pm": "PM", "selectTime": "Select Time", "selectDate": "Select Date", "selectCategory": "Select Category", "selectContact": "Select Contact", "update": "Update", "updated": "Updated", "create": "Create", "created": "Created", "remove": "Remove", "removed": "Removed", "add": "Add", "added": "Added", "peopleUsingContactTimes": "People using Contact Times", "permissionRequired": "Permission Required", "somethingWentWrong": "Something went wrong", "openSettings": "Open Settings", "noContactsUsingApp": "No contacts using the app", "noContactsUsingAppDescription": "None of your contacts are using Contact Times yet.\n\nTo see contacts here:\n• Invite friends to download the app\n• Make sure they sign up with their phone numbers\n• Their numbers should be in your device contacts", "contactsCount": "{count} contact{plural}", "@contactsCount": {"placeholders": {"count": {"type": "int"}, "plural": {"type": "String"}}}, "assignCategory": "Assign Category", "loadingEllipsis": "Loading...", "errorLoadingCategories": "Error loading categories: {error}", "@errorLoadingCategories": {"placeholders": {"error": {"type": "String"}}}, "@contactAssignedToCategory": {"placeholders": {"contactName": {"type": "String"}, "categoryName": {"type": "String"}}}, "assignmentMayHaveFailed": "Assignment may have failed - please try again", "errorAssigningCategory": "Error assigning category: {error}", "@errorAssigningCategory": {"placeholders": {"error": {"type": "String"}}}, "viewDetails": "View details", "failedToMakeCall": "Failed to make call: {error}", "@failedToMakeCall": {"placeholders": {"error": {"type": "String"}}}, "notificationEnabledOffline": "Notification enabled for this time slot (offline)", "notificationDisabledOffline": "Notification disabled for this time slot (offline)", "notificationEnabled": "Notification enabled for this time slot", "notificationDisabled": "Notification disabled for this time slot", "errorUpdatingNotification": "Error updating notification", "notificationPreferenceExists": "Notification preference already exists. Please try again.", "networkErrorCheckConnection": "Network error. Please check your connection and try again.", "callContactQuestion": "Call {contactName}?", "@callContactQuestion": {"placeholders": {"contactName": {"type": "String"}}}, "goodTimeToCall": "Good time to call", "notIdealTiming": "Not ideal timing", "theirPreferredTimes": "Their preferred times:", "noTimeSlotsAvailable": "No time slots available", "callNow": "Call Now", "callAnyway": "Call Anyway", "activeNowLabel": "ACTIVE NOW", "on": "ON", "off": "OFF", "chooseHowToCategorize": "Choose how to categorize {contactName}", "@chooseHowToCategorize": {"placeholders": {"contactName": {"type": "String"}}}, "availableCategories": "Available Categories:", "loadingContactDetails": "Loading contact details...", "contact": "Contact", "contactPreferences": "{contactName}'s Preferences", "@contactPreferences": {"placeholders": {"contactName": {"type": "String"}}}, "communicationPreferences": "Communication preferences", "callingSuggestion": "Calling Suggestion", "noPreferencesSet": "No preferences set", "contactHasntSetPreferences": "This contact hasn't set communication preferences for you yet.", "currentUserProfileNotFound": "Current user profile not found", "errorUpdatingNotificationWithDetails": "Error updating notification: {error}", "@errorUpdatingNotificationWithDetails": {"placeholders": {"error": {"type": "String"}}}, "categoryContactAnytime": "Anytime", "categoryPreferAnytime": "If It Can Wait", "categoryContactAtTimes": "Preferred Times", "categoryContactThroughMessages": "Messages Only", "categoryNoteContactAnytime": "Feel free to contact me at any time.", "categoryNotePreferAnytime": "You can reach out anytime if it's not urgent, but I prefer to be contacted during the times below when possible.", "categoryNoteContactAtTimes": "Please contact me during the preferred time slots listed below.", "categoryNoteContactThroughMessages": "I prefer to be contacted by message only, and during the times shown below.", "callingSuggestionNoCategory": "This contact hasn't set up Contact Times yet.", "callingSuggestionContactAnytime": "You can contact them at any time.", "callingSuggestionPreferAnytimeGoodTime": "Now is a preferred time to call.", "callingSuggestionPreferAnytimeBadTime": "You can call now, but they prefer calls during their specified times.", "callingSuggestionContactAtTimesGoodTime": "Now is a good time to call.", "callingSuggestionContactAtTimesBadTime": "Please call during their specified times.", "callingSuggestionContactThroughMessages": "They prefer to be contacted through messages.", "contactSuggestionNoCategory": "This contact hasn't set communication preferences yet. You can contact them anytime.", "contactSuggestionContactAnytimeGood": "✅ Great time to contact! {note}", "@contactSuggestionContactAnytimeGood": {"placeholders": {"note": {"type": "String"}}}, "contactSuggestionPreferAnytimeGoodTime": "✅ Perfect time to contact! You're calling during their preferred hours.", "contactSuggestionPreferAnytimeBadTime": "⚠️ You can contact them now, but they prefer calls during their specified times.", "contactSuggestionContactAtTimesGoodTime": "✅ Perfect time to contact! You're calling during their available hours.", "contactSuggestionContactAtTimesBadTime": "❌ Not the best time. They prefer to be contacted during their specified times only.", "contactSuggestionContactThroughMessages": "💬 They prefer messages over calls. Consider sending a text instead.", "azNavigation": "أ-Z", "@azNavigation": {"description": "Compact alphabet navigation button text for both English and Arabic"}, "@searchContacts": {"description": "Placeholder text for contact search input field"}, "recentSearches": "Recent searches", "@recentSearches": {"description": "Header text for search history dropdown"}, "@clear": {"description": "Button text to clear search history"}, "noSearchResults": "No search results", "@noSearchResults": {"description": "Title when no contacts match search query"}, "noSearchResultsDescription": "No contacts found matching \"{query}\".\n\nTry:\n• Checking the spelling\n• Using a different search term\n• Searching by name or phone number", "@noSearchResultsDescription": {"description": "Description when no contacts match search query", "placeholders": {"query": {"type": "String", "description": "The search query that returned no results"}}}, "clearSearch": "Clear search", "@clearSearch": {"description": "Button text to clear current search"}, "searchResults": "{count} of {total} contacts", "@searchResults": {"description": "Text showing filtered search results count", "placeholders": {"count": {"type": "int", "description": "Number of contacts matching search"}, "total": {"type": "int", "description": "Total number of contacts"}}}, "jumpToLetter": "Jump to Letter", "@jumpToLetter": {"description": "Header text for alphabet navigation dropdown"}, "@noContactsAvailable": {"description": "Message shown when no contacts are available for alphabet navigation"}, "share": "Share", "shareApp": "Share App", "chooseHowToShare": "Choose how you'd like to share the app with {contactName}:", "@chooseHowToShare": {"placeholders": {"contactName": {"type": "String"}}}, "whatsapp": "WhatsApp", "sms": "SMS", "otherApps": "Other Apps", "copyLink": "Copy Link", "whatsappOpenedWithMessage": "WhatsApp opened. Message copied to clipboard - paste it to share!", "shareMessage": "📱 I'm using Contact Time to manage when people can call or message me — no more missed or mistimed calls!\n\nWant to know the best time to reach me? Download Contact Time and search for my profile to see when I'm available.\n\n👉 {appLink}\n\nSet your own contact preferences too — it's easy and stress-free.", "@shareMessage": {"description": "Template message for sharing the app with contacts", "placeholders": {"appLink": {"type": "String", "description": "The app download link"}}}, "notUsingTheApp": "Not using the app", "contactNotUsingAppYet": "{contactName} is not using this app yet.", "@contactNotUsingAppYet": {"placeholders": {"contactName": {"type": "String"}}}, "whatWouldYouLikeToDo": "What would you like to do?", "selectPhoneNumber": "Select Phone Number", "choosePhoneNumberToCall": "Choose which phone number to call:", "profileUpdatedOffline": "Profile updated offline. Will sync when online.", "offlineMode": "Offline Mode", "profileUpdateOnlineOnly": "Profile updates and image uploads are only available when you're connected to the internet. Please check your connection and try again.", "backOnline": "You're back online! You can now update your profile.", "stillOffline": "Still offline. Please check your internet connection.", "emergencyMode": "Emergency Mode", "activateEmergencyMode": "Activate Emergency Mode", "emergencyModeActive": "Emergency Mode Active", "emergencyModeDescription": "During emergency mode, contacts will see a special message when trying to call or message you.", "selectDuration": "Select Duration:", "hour1": "Hour 1", "hours3": "Hours 3", "hours6": "Hours 6", "hours12": "Hours 12", "day1": "Day 1", "week1": "Week 1", "understood": "Understood", "notAvailable": "not available", "personalEmergency": "personal emergency", "emergencyModeActiveFor": "Emergency Mode Active for {contactName}", "@emergencyModeActiveFor": {"placeholders": {"contactName": {"type": "String"}}}, "contactNotAvailable": "{contactName} is not available", "@contactNotAvailable": {"placeholders": {"contactName": {"type": "String"}}}, "emergencyModeMessage": "{userName} is currently in emergency mode due to {reason}. They are not available for calls or messages. Emergency mode: {remaining}", "@emergencyModeMessage": {"placeholders": {"userName": {"type": "String"}, "reason": {"type": "String"}, "remaining": {"type": "String"}}}, "daysRemaining": "{days}d {hours}h remaining", "@daysRemaining": {"placeholders": {"days": {"type": "int"}, "hours": {"type": "int"}}}, "hoursRemaining": "{hours}h {minutes}m remaining", "@hoursRemaining": {"placeholders": {"hours": {"type": "int"}, "minutes": {"type": "int"}}}, "minutesRemaining": "{minutes}m remaining", "@minutesRemaining": {"placeholders": {"minutes": {"type": "int"}}}}