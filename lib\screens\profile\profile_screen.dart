import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import '../../models/models.dart';
import '../../services/supabase_service.dart';
import '../../services/language_service.dart';
import '../../services/offline_contact_service.dart';
import '../../services/connectivity_service.dart';
import '../../services/local_database_service.dart';
import '../auth/sign_in_screen.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../widgets/no_internet_widget.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> with TickerProviderStateMixin {
  Profile? _profile;
  bool _isLoading = true;
  String? _error;
  String? _cachedImagePath; // Track cached image path

  final _fullNameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _phoneController = TextEditingController();

  AnimationController? _fadeController;
  AnimationController? _slideController;
  Animation<double>? _fadeAnimation;
  Animation<Offset>? _slideAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController!,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController!,
      curve: Curves.easeOutBack,
    ));

    _loadProfile();
  }

  @override
  void dispose() {
    _fadeController?.dispose();
    _slideController?.dispose();
    _fullNameController.dispose();
    _usernameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  /// Force refresh profile data from local storage
  /// Note: This only refreshes profile data, not language preferences
  /// Language preferences are stored in SharedPreferences separately
  Future<void> _refreshProfileData() async {
    final offlineService = OfflineContactService();

    // Clear cache and get fresh data from database
    await offlineService.invalidateProfileCache();
    final refreshedProfile = await offlineService.refreshCurrentProfile();

    if (mounted && refreshedProfile != null) {
      setState(() {
        _profile = refreshedProfile;
        // Update form fields with refreshed data
        _fullNameController.text = _profile?.fullName ?? '';
        _usernameController.text = _profile?.username ?? '';
        _phoneController.text = _profile?.phoneNumber ?? '';
      });
    }
  }

  /// Delete old cached images for the current user
  Future<void> _deleteOldCachedImages() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final cacheDir = Directory('${directory.path}/profile_images');

      if (!await cacheDir.exists()) {
        return;
      }

      // Find and delete all cached images for this user
      final files = await cacheDir.list().toList();
      for (final file in files) {
        if (file is File && file.path.contains(_profile!.id)) {
          await file.delete();
          print('📷 Deleted old cached image: ${file.path}');
        }
      }
    } catch (e) {
      print('❌ Failed to delete old cached images: $e');
    }
  }

  /// Cache image locally for offline access
  Future<String> _cacheImageLocally(String sourcePath, String networkUrl) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final cacheDir = Directory('${directory.path}/profile_images');

      // Create cache directory if it doesn't exist
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      // Delete old cached images first
      await _deleteOldCachedImages();

      // Generate a unique filename based on the user ID and timestamp
      final fileName = '${_profile!.id}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final localPath = '${cacheDir.path}/$fileName';

      // Copy the image to cache directory
      final sourceFile = File(sourcePath);
      final cachedFile = await sourceFile.copy(localPath);

      print('📷 Image cached: $sourcePath -> $localPath');
      return cachedFile.path;
    } catch (e) {
      print('❌ Failed to cache image locally: $e');
      return sourcePath; // Return original path if caching fails
    }
  }

  /// Download and cache network image for offline access
  Future<String?> _downloadAndCacheNetworkImage(String networkUrl) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final cacheDir = Directory('${directory.path}/profile_images');

      // Create cache directory if it doesn't exist
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      // Delete old cached images first
      await _deleteOldCachedImages();

      // Download the image from network
      final response = await http.get(Uri.parse(networkUrl));
      if (response.statusCode == 200) {
        // Generate filename for cached image
        final fileName = '${_profile!.id}_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final localPath = '${cacheDir.path}/$fileName';

        // Save the downloaded image to cache
        final cachedFile = File(localPath);
        await cachedFile.writeAsBytes(response.bodyBytes);

        print('📷 Downloaded and cached network image: $networkUrl -> $localPath');
        return localPath;
      } else {
        print('❌ Failed to download image: HTTP ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('❌ Failed to download and cache network image: $e');
      return null;
    }
  }

  /// Get cached image path for a network URL
  Future<String?> _getCachedImagePath(String networkUrl) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final cacheDir = Directory('${directory.path}/profile_images');

      if (!await cacheDir.exists()) {
        return null;
      }

      // Look for cached images for this user
      final files = await cacheDir.list().toList();
      for (final file in files) {
        if (file is File && file.path.contains(_profile!.id)) {
          // Check if file exists and is not empty
          if (await file.exists() && await file.length() > 0) {
            print('📷 Found cached image: ${file.path}');
            return file.path;
          }
        }
      }

      return null;
    } catch (e) {
      print('❌ Failed to get cached image path: $e');
      return null;
    }
  }

  Future<void> _loadProfile() async {
    try {
      if (mounted) {
        setState(() {
          _isLoading = true;
          _error = null;
        });
      }

      // Use offline-capable service to get profile
      final offlineService = OfflineContactService();

      // Force refresh from database to get latest data
      await offlineService.invalidateProfileCache();
      final profile = await offlineService.refreshCurrentProfile();

      if (profile != null) {
        // Check for cached image
        if (profile.avatarUrl != null) {
          _cachedImagePath = await _getCachedImagePath(profile.avatarUrl!);

          // If no cached image exists and we have a network URL, download and cache it
          if (_cachedImagePath == null && profile.avatarUrl!.startsWith('http')) {
            _cachedImagePath = await _downloadAndCacheNetworkImage(profile.avatarUrl!);
          }
        }

        if (mounted) {
          setState(() {
            _profile = profile;
            _fullNameController.text = profile.fullName ?? '';
            _usernameController.text = profile.username ?? '';
            _phoneController.text = profile.phoneNumber;
            _isLoading = false;
          });

          // Start animations after loading
          _fadeController?.forward();
          Future.delayed(const Duration(milliseconds: 200), () {
            if (mounted) {
              _slideController?.forward();
            }
          });
        }
      } else {
        if (mounted) {
          setState(() {
            _error = AppLocalizations.of(context)?.profileNotFound ?? 'Profile not found';
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateProfile() async {
    if (_profile == null) return;

    // Check if online
    final connectivityService = ConnectivityService();
    if (!connectivityService.isOnline) {
      _showOfflineDialog();
      return;
    }

    try {
      final updatedProfile = _profile!.copyWith(
        fullName: _fullNameController.text.trim().isEmpty
            ? null
            : _fullNameController.text.trim(),
        username: _usernameController.text.trim().isEmpty
            ? null
            : _usernameController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        updatedAt: DateTime.now(),
      );

      // Update profile online only
      final updatedServerProfile = await SupabaseService.updateProfile(updatedProfile);
      print('📝 Profile updated on server: ${updatedServerProfile.fullName}, ${updatedServerProfile.username}');

      // Save to local database for caching
      await LocalDatabaseService.insertProfile(updatedServerProfile);
      print('📝 Profile saved to local database: ${updatedServerProfile.fullName}, ${updatedServerProfile.username}');

      // Refresh the offline service cache to ensure consistency
      final offlineService = OfflineContactService();
      await offlineService.invalidateProfileCache();
      final refreshedProfile = await offlineService.refreshCurrentProfile();
      print('📝 Refreshed profile from database: ${refreshedProfile?.fullName}, ${refreshedProfile?.username}');

      if (mounted) {
        setState(() {
          _profile = refreshedProfile ?? updatedServerProfile;
          // Update form fields with refreshed data
          _fullNameController.text = _profile?.fullName ?? '';
          _usernameController.text = _profile?.username ?? '';
          _phoneController.text = _profile?.phoneNumber ?? '';
        });
      }

      if (mounted) {
        // Check if we're online to show appropriate message
        final connectivityService = ConnectivityService();
        final isOnline = connectivityService.isOnline;

        final message = isOnline
            ? (AppLocalizations.of(context)?.profileUpdatedSuccessfully ?? 'Profile updated successfully')
            : (AppLocalizations.of(context)?.profileUpdatedOffline ?? 'Profile updated offline. Will sync when online.');

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context)?.profileUpdateFailed ?? 'Failed to update profile'}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _signOut() async {
    try {
      await SupabaseService.signOut();
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const SignInScreen()),
          (route) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context)?.failedToSignOut ?? 'Failed to sign out'}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context)?.profile ?? 'Profile',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 24,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        iconTheme: const IconThemeData(color: Colors.white),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF667eea),
                Color(0xFF764ba2),
              ],
            ),
          ),
        ),
        // actions: [
        //   Container(
        //     margin: const EdgeInsets.only(right: 16),
        //     child: IconButton(
        //       icon: const Icon(Icons.logout, color: Colors.white),
        //       onPressed: _signOut,
        //       style: IconButton.styleFrom(
        //         backgroundColor: Colors.white.withOpacity(0.2),
        //         shape: RoundedRectangleBorder(
        //           borderRadius: BorderRadius.circular(12),
        //         ),
        //       ),
        //     ),
        //   ),
        // ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
            ],
          ),
        ),
        child: SafeArea(
          child: _buildBody(),
        ),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return Center(
        child: Container(
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.95),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                spreadRadius: 0,
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                  ),
                  borderRadius: BorderRadius.circular(50),
                ),
                child: const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 3,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                AppLocalizations.of(context)?.loadingProfile ?? 'Loading Profile...',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF2D3748),
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_error != null) {
      // Check if it's a network-related error
      final errorMessage = _error!.toLowerCase();
      final isNetworkError = errorMessage.contains('clientexception') ||
          errorMessage.contains('socketexception') ||
          errorMessage.contains('failed host lookup') ||
          errorMessage.contains('network') ||
          errorMessage.contains('connection') ||
          errorMessage.contains('internet');

      if (isNetworkError) {
        return NoInternetWidget(
          onRetry: _loadProfile,
        );
      } else {
        // Show regular error for non-network errors
        return Center(
          child: Container(
            margin: const EdgeInsets.all(32),
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.95),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  spreadRadius: 0,
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: Icon(
                    Icons.error_outline,
                    size: 48,
                    color: Colors.red[400],
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  AppLocalizations.of(context)?.oopsSomethingWentWrong ?? 'Oops! Something went wrong',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2D3748),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  _error!,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: _loadProfile,
                  icon: const Icon(Icons.refresh, size: 20),
                  label: Text(
                    AppLocalizations.of(context)?.tryAgain ?? 'Try Again',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF667eea),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                ),
              ],
            ),
          ),
        );
      }
    }

    // Only apply animations if they're initialized
    Widget content = RefreshIndicator(
      onRefresh: _refreshProfileData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        physics: const AlwaysScrollableScrollPhysics(), // Ensures pull-to-refresh works even when content doesn't scroll
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
          // Profile Picture Section
          Center(
            child: _buildProfileHeader(),
          ),

          const SizedBox(height: 24),

          // Profile Form
          _buildProfileForm(),

          const SizedBox(height: 20),

          // App Information Card
          _buildAppInfoCard(),

          const SizedBox(height: 20),

          // Action Buttons
          _buildActionButtons(),

          const SizedBox(height: 32),
        ],
        ),
      ),
    );

    // Apply animations if they're available
    if (_fadeAnimation != null && _slideAnimation != null) {
      return FadeTransition(
        opacity: _fadeAnimation!,
        child: SlideTransition(
          position: _slideAnimation!,
          child: content,
        ),
      );
    }

    return content;
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.95),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Profile Avatar
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF667eea).withOpacity(0.3),
                  spreadRadius: 0,
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: CircleAvatar(
              key: ValueKey(_profile?.avatarUrl ?? 'no_avatar'), // Force rebuild when avatar changes
              radius: 60,
              backgroundColor: const Color(0xFF667eea),
              backgroundImage: _getAvatarImageProvider(),
              child: _getAvatarImageProvider() == null
                  ? Text(
                      _profile?.fullName?.isNotEmpty == true
                          ? _profile!.fullName![0].toUpperCase()
                          : _profile?.username?.isNotEmpty == true
                              ? _profile!.username![0].toUpperCase()
                              : '?',
                      style: const TextStyle(
                        fontSize: 36,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    )
                  : null,
            ),
          ),

          const SizedBox(height: 20),

          // Profile Name
          Text(
            _profile?.fullName?.isNotEmpty == true
                ? _profile!.fullName!
                : _profile?.username?.isNotEmpty == true
                    ? _profile!.username!
                    : (AppLocalizations.of(context)?.welcome ?? 'Welcome!'),
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2D3748),
            ),
          ),

          const SizedBox(height: 8),

          // Phone Number
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF667eea), Color(0xFF764ba2)],
              ),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.phone,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Directionality(
                  textDirection: TextDirection.ltr,
                  child: Text(
                    _profile?.phoneNumber ?? (AppLocalizations.of(context)?.noPhoneNumber ?? 'No phone number'),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Change Photo Button
          TextButton.icon(
            onPressed: _showImageSourceDialog,
            icon: const Icon(
              Icons.camera_alt,
              color: Color(0xFF667eea),
              size: 20,
            ),
            label: Text(
              AppLocalizations.of(context)?.changePhoto ?? 'Change Photo',
              style: const TextStyle(
                color: Color(0xFF667eea),
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            style: TextButton.styleFrom(
              backgroundColor: const Color(0xFF667eea).withOpacity(0.1),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileForm() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.95),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.edit,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                AppLocalizations.of(context)?.profileInformation ?? 'Profile Information',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2D3748),
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          _buildModernTextField(
            controller: _fullNameController,
            label: AppLocalizations.of(context)?.fullName ?? 'Full Name',
            icon: Icons.person,
            hint: AppLocalizations.of(context)?.enterYourFullName ?? 'Enter your full name',
          ),

          const SizedBox(height: 20),

          _buildModernTextField(
            controller: _usernameController,
            label: AppLocalizations.of(context)?.username ?? 'Username',
            icon: Icons.alternate_email,
            hint: AppLocalizations.of(context)?.enterYourUsername ?? 'Choose a unique username',
          ),

          const SizedBox(height: 20),

          _buildModernTextField(
            controller: _phoneController,
            label: AppLocalizations.of(context)?.phoneNumber ?? 'Phone Number',
            icon: Icons.phone,
            hint: AppLocalizations.of(context)?.enterYourPhoneNumber ?? 'Enter your phone number',
            keyboardType: TextInputType.phone,
            isPhoneNumber: true,
          ),

          const SizedBox(height: 32),

          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _updateProfile,
              icon: const Icon(Icons.save, size: 20),
              label: Text(
                AppLocalizations.of(context)?.updateProfile ?? 'Update Profile',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667eea),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required String hint,
    TextInputType? keyboardType,
    bool isPhoneNumber = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: isPhoneNumber
            ? Directionality(
                textDirection: TextDirection.ltr,
                child: TextFormField(
                  controller: controller,
                  keyboardType: keyboardType,
                  decoration: InputDecoration(
                    hintText: hint,
                    prefixIcon: Icon(
                      icon,
                      color: const Color(0xFF667eea),
                      size: 20,
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                    hintStyle: TextStyle(
                      color: Colors.grey[400],
                      fontSize: 14,
                    ),
                  ),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF2D3748),
                  ),
                ),
              )
            : TextFormField(
                controller: controller,
                keyboardType: keyboardType,
                decoration: InputDecoration(
                  hintText: hint,
                  prefixIcon: Icon(
                    icon,
                    color: const Color(0xFF667eea),
                    size: 20,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  hintStyle: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 14,
                  ),
                ),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF2D3748),
                ),
              ),
        ),
      ],
    );
  }

  Widget _buildAppInfoCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.95),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.info,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                AppLocalizations.of(context)?.aboutContactTimes ?? 'About Contact Times',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2D3748),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Text(
            AppLocalizations.of(context)?.manageWhenYouCanBeContacted ?? 'Manage when you can be contacted by different people. Set up categories and time slots to let others know your availability preferences.',
            style: TextStyle(
              fontSize: 15,
              color: Colors.grey[600],
              height: 1.5,
            ),
          ),

          const SizedBox(height: 20),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF667eea).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF667eea).withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.verified,
                  color: Color(0xFF667eea),
                  size: 20,
                ),
                const SizedBox(width: 12),
                Text(
                  '${AppLocalizations.of(context)?.version ?? 'Version'}: ',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF2D3748),
                  ),
                ),
                Text(
                  '1.0.0',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showImageSourceDialog() {
    showDialog(
      context: context,
      builder: (context) => _ImageSourceDialog(
        onImageSelected: _uploadProfilePicture,
      ),
    );
  }

  Future<void> _uploadProfilePicture(ImageSource source) async {
    // Check if online first
    final connectivityService = ConnectivityService();
    if (!connectivityService.isOnline) {
      _showOfflineDialog();
      return;
    }

    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: source,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (pickedFile == null) return;

      if (mounted) {
        setState(() {
          _isLoading = true;
        });
      }

      // Upload image to server (online only)
      final imageUrl = await SupabaseService.uploadProfilePicture(
        pickedFile.path,
        _profile!.id,
      );
      print('📷 Image uploaded successfully: $imageUrl');

      // Cache the image locally for offline access (from the uploaded file)
      final localImagePath = await _cacheImageLocally(pickedFile.path, imageUrl);
      print('📷 Image cached locally at: $localImagePath');

      // Update cached image path for immediate use
      _cachedImagePath = localImagePath;

      // Update profile with new avatar URL
      final updatedProfile = _profile!.copyWith(
        avatarUrl: imageUrl,
        updatedAt: DateTime.now(),
      );
      print('📷 Updated profile with avatar URL: ${updatedProfile.avatarUrl}');

      // Update profile on server
      final savedProfile = await SupabaseService.updateProfile(updatedProfile);
      print('📷 Profile updated on server: ${savedProfile.avatarUrl}');

      // Save to local database for caching
      await LocalDatabaseService.insertProfile(savedProfile);
      print('📷 Profile saved to local database: ${savedProfile.avatarUrl}');

      // Refresh the offline service cache to ensure consistency
      final offlineService = OfflineContactService();
      await offlineService.invalidateProfileCache();
      final refreshedProfile = await offlineService.refreshCurrentProfile();
      print('📷 Refreshed profile from database: ${refreshedProfile?.avatarUrl}');

      if (mounted) {
        // Clear image cache to force reload of new image
        if (_profile?.avatarUrl != null) {
          final oldImageProvider = NetworkImage(_profile!.avatarUrl!);
          oldImageProvider.evict();
        }

        setState(() {
          _profile = refreshedProfile ?? savedProfile;
          _isLoading = false;
        });

        // Force a rebuild to ensure the new image is displayed
        print('📷 UI updated with new profile: ${_profile?.avatarUrl}');

        final message = AppLocalizations.of(context)?.imageUploadedSuccessfully ?? 'Profile picture updated successfully';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context)?.failedToUploadImage ?? 'Failed to upload image'}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Helper method to get the appropriate image provider for avatar
  ImageProvider? _getAvatarImageProvider() {
    if (_profile?.avatarUrl == null) return null;

    final avatarUrl = _profile!.avatarUrl!;

    // Check if it's a local file path (starts with / or contains cache/)
    if (avatarUrl.startsWith('/') || avatarUrl.contains('cache/')) {
      final file = File(avatarUrl);
      if (file.existsSync()) {
        return FileImage(file);
      }
      // If local file doesn't exist, return null to show initials
      return null;
    }

    // Check if we have a cached image first
    if (_cachedImagePath != null) {
      final cachedFile = File(_cachedImagePath!);
      if (cachedFile.existsSync()) {
        print('📷 Using cached image: $_cachedImagePath');
        return FileImage(cachedFile);
      }
    }

    // It's a network URL - add cache busting parameter to force refresh
    final cacheBustingUrl = '$avatarUrl?v=${DateTime.now().millisecondsSinceEpoch}';
    print('📷 Loading image from URL: $cacheBustingUrl');
    return NetworkImage(cacheBustingUrl);
  }

  /// Show modern offline dialog
  void _showOfflineDialog() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon with gradient background
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.orange.withOpacity(0.2),
                        Colors.red.withOpacity(0.2),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(40),
                  ),
                  child: Icon(
                    Icons.wifi_off_rounded,
                    size: 40,
                    color: Colors.orange.shade600,
                  ),
                ),

                const SizedBox(height: 24),

                // Title
                Text(
                  AppLocalizations.of(context)?.offlineMode ?? 'Offline Mode',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.headlineSmall?.color,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 12),

                // Description
                Text(
                  AppLocalizations.of(context)?.profileUpdateOnlineOnly ??
                  'Profile updates and image uploads are only available when you\'re connected to the internet. Please check your connection and try again.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 32),

                // Action buttons
                Row(
                  children: [
                    // Cancel button
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                            side: BorderSide(
                              color: Theme.of(context).dividerColor,
                              width: 1,
                            ),
                          ),
                        ),
                        child: Text(
                          AppLocalizations.of(context)?.cancel ?? 'Cancel',
                          style: TextStyle(
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // Retry button
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          // Check connectivity again
                          final connectivityService = ConnectivityService();
                          if (connectivityService.isOnline) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  AppLocalizations.of(context)?.backOnline ?? 'You\'re back online! You can now update your profile.',
                                ),
                                backgroundColor: Colors.green,
                              ),
                            );
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  AppLocalizations.of(context)?.stillOffline ?? 'Still offline. Please check your internet connection.',
                                ),
                                backgroundColor: Colors.orange,
                              ),
                            );
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF667eea),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        child: Text(
                          AppLocalizations.of(context)?.retry ?? 'Retry',
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder: (context) => _DeleteAccountDialog(
        onConfirmDelete: _deleteAccount,
      ),
    );
  }

  Future<void> _deleteAccount() async {
    try {
      setState(() {
        _isLoading = true;
      });

      await SupabaseService.deleteAccount();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)?.accountDeletedSuccessfully ?? 'Account deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate to sign in screen
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const SignInScreen()),
          (route) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context)?.failedToDeleteAccount ?? 'Failed to delete account'}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showLanguageDialog() {
    final languageService = LanguageService();
    final supportedLanguages = languageService.getSupportedLanguages();

    showDialog(
      context: context,
      builder: (context) => _LanguageSelectionDialog(
        supportedLanguages: supportedLanguages,
        languageService: languageService,
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // Delete Account Button - Moved to top for safety
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _showDeleteAccountDialog,
            icon: const Icon(Icons.delete_forever, color: Colors.red, size: 20),
            label: Text(
              AppLocalizations.of(context)?.deleteAccount ?? 'Delete Account',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.red,
              ),
            ),
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: Colors.red, width: 2),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),

        const SizedBox(height: 32), // Increased spacing for safety

        // Language Selection Button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _showLanguageDialog,
            icon: const Icon(Icons.language, size: 20),
            label: Text(
              AppLocalizations.of(context)?.language ?? 'Language',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF764ba2),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
          ),
        ),

        const SizedBox(height: 32), // Increased spacing for safety

        // Sign Out Button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _signOut,
            icon: const Icon(Icons.logout, color: Colors.red, size: 20),
            label: Text(
              AppLocalizations.of(context)?.signOut ?? 'Sign Out',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.red,
              ),
            ),
            style: OutlinedButton.styleFrom(
              backgroundColor: Colors.red[50],
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: BorderSide(color: Colors.red[300]!),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _LanguageSelectionDialog extends StatelessWidget {
  final List<Map<String, String>> supportedLanguages;
  final LanguageService languageService;

  const _LanguageSelectionDialog({
    required this.supportedLanguages,
    required this.languageService,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.6,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.white.withOpacity(0.95),
            ],
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF667eea).withOpacity(0.3),
              blurRadius: 25,
              offset: const Offset(0, 12),
              spreadRadius: 2,
            ),
            BoxShadow(
              color: Colors.white.withOpacity(0.8),
              blurRadius: 10,
              offset: const Offset(-5, -5),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with icon and title
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF667eea).withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.language,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      AppLocalizations.of(context)?.selectLanguage ?? 'Select Language',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        color: Colors.grey[800],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Language options
              Flexible(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: supportedLanguages.map((language) {
                    final isSelected = languageService.currentLocale.languageCode == language['code'];
                    return _buildLanguageOption(context, language, isSelected);
                  }).toList(),
                ),
              ),

              const SizedBox(height: 24),

              // Cancel button
              SizedBox(
                width: double.infinity,
                child: TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                  child: Text(
                    AppLocalizations.of(context)?.cancel ?? 'Cancel',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageOption(BuildContext context, Map<String, String> language, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () async {
            final navigator = Navigator.of(context);
            await languageService.changeLanguage(language['code']!);
            navigator.pop();
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  isSelected ? const Color(0xFF667eea).withOpacity(0.1) : Colors.grey.shade50,
                  isSelected ? const Color(0xFF764ba2).withOpacity(0.1) : Colors.grey.shade100,
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected ? const Color(0xFF667eea).withOpacity(0.3) : Colors.grey.shade200,
                width: isSelected ? 2 : 1,
              ),
              boxShadow: [
                if (isSelected) ...[
                  BoxShadow(
                    color: const Color(0xFF667eea).withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ] else ...[
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    gradient: isSelected
                        ? const LinearGradient(
                            colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                          )
                        : null,
                    color: isSelected ? null : Colors.grey.shade300,
                    shape: BoxShape.circle,
                    boxShadow: [
                      if (isSelected)
                        BoxShadow(
                          color: const Color(0xFF667eea).withOpacity(0.4),
                          blurRadius: 4,
                          offset: const Offset(0, 1),
                        ),
                    ],
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 14,
                        )
                      : null,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        language['nativeName']!,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isSelected ? const Color(0xFF667eea) : Colors.grey[800],
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        language['name']!,
                        style: TextStyle(
                          fontSize: 14,
                          color: isSelected ? const Color(0xFF667eea).withOpacity(0.8) : Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _ImageSourceDialog extends StatelessWidget {
  final Function(ImageSource) onImageSelected;

  const _ImageSourceDialog({
    required this.onImageSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.white.withOpacity(0.95),
            ],
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF667eea).withOpacity(0.3),
              blurRadius: 25,
              offset: const Offset(0, 12),
              spreadRadius: 2,
            ),
            BoxShadow(
              color: Colors.white.withOpacity(0.8),
              blurRadius: 10,
              offset: const Offset(-5, -5),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with icon and title
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF667eea).withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      AppLocalizations.of(context)?.selectImageSource ?? 'Select Image Source',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        color: Colors.grey[800],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Camera option
              _buildSourceOption(
                context,
                icon: Icons.camera_alt,
                title: AppLocalizations.of(context)?.camera ?? 'Camera',
                onTap: () {
                  Navigator.of(context).pop();
                  onImageSelected(ImageSource.camera);
                },
              ),

              const SizedBox(height: 16),

              // Gallery option
              _buildSourceOption(
                context,
                icon: Icons.photo_library,
                title: AppLocalizations.of(context)?.gallery ?? 'Gallery',
                onTap: () {
                  Navigator.of(context).pop();
                  onImageSelected(ImageSource.gallery);
                },
              ),

              const SizedBox(height: 24),

              // Cancel button
              SizedBox(
                width: double.infinity,
                child: TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                  child: Text(
                    AppLocalizations.of(context)?.cancel ?? 'Cancel',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSourceOption(BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: onTap,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.grey.shade50, Colors.grey.shade100],
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.grey.shade200),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _DeleteAccountDialog extends StatelessWidget {
  final VoidCallback onConfirmDelete;

  const _DeleteAccountDialog({
    required this.onConfirmDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.white.withOpacity(0.95),
            ],
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.red.withOpacity(0.3),
              blurRadius: 25,
              offset: const Offset(0, 12),
              spreadRadius: 2,
            ),
            BoxShadow(
              color: Colors.white.withOpacity(0.8),
              blurRadius: 10,
              offset: const Offset(-5, -5),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with icon and title
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colors.red, Colors.redAccent],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.red.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.warning,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      AppLocalizations.of(context)?.deleteAccount ?? 'Delete Account',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        color: Colors.grey[800],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Warning message
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.red.shade50, Colors.red.shade100],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.red.shade200, width: 1.5),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.red.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context)?.deleteAccountConfirmation ?? 'Are you sure you want to delete your account? This action cannot be undone.',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.red[800],
                        height: 1.5,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      AppLocalizations.of(context)?.deleteAccountWarning ?? 'This will permanently delete your account and all associated data.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.red[700],
                        height: 1.4,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      child: Text(
                        AppLocalizations.of(context)?.cancel ?? 'Cancel',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        onConfirmDelete();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red[600],
                        foregroundColor: Colors.white,
                        elevation: 8,
                        shadowColor: Colors.red.withOpacity(0.3),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.delete_forever, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            AppLocalizations.of(context)?.confirmDelete ?? 'Confirm Delete',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
